package scrbg.meplat.mallauth.service;

import scrbg.meplat.mallauth.entity.SysMenu;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mallauth.entity.SysMenu;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import scrbg.meplat.mallauth.entity.SysRole;
import scrbg.meplat.mallauth.vo.ChangStateVo;

import java.util.List;
/**
 * @描述：菜单表 服务类
 * @作者: ye
 * @日期: 2023-12-20
 */
public interface SysMenuService extends IService<SysMenu> {
        List<SysMenu> queryPage(JSONObject jsonObject, LambdaQueryWrapper<SysMenu> queryWrapper);

        void create(SysMenu sysMenu);
        void update(SysMenu sysMenu);
        SysMenu getById(String id);

        void delete(String id);

        /**
         * 批量删除菜单
         * @param ids
         */
        void deleteBatch( List<String> ids);
        /**
         * 修改菜单展示状态  （1显示  0不显示）

         */
        void updateState(ChangStateVo changStateVo);

        /**
         * 查询父级菜单
         * @param jsonObject
         * @param sysMenuLambdaQueryWrapper
         * @return
         */
        List<SysMenu> selectParentMenus(JSONObject jsonObject, LambdaQueryWrapper<SysMenu> sysMenuLambdaQueryWrapper);

        /**
         * 修改菜单是否测试 （1显示  0不显示）
         * @param changStateVo
         */
        void updateShowDev(ChangStateVo changStateVo);


        void changeSortValue(List<SysMenu> ids);
}
