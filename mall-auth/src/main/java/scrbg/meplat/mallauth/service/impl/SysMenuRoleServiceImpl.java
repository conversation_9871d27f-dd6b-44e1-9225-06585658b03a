package scrbg.meplat.mallauth.service.impl;

import scrbg.meplat.mallauth.entity.SysMenuRole;
import scrbg.meplat.mallauth.mapper.SysMenuRoleMapper;
import scrbg.meplat.mallauth.service.SysMenuRoleService;
import scrbg.meplat.mallauth.config.redis.redisson.NotResubmit;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @描述：菜单角色表 服务类
 * @作者: ye
 * @日期: 2023-12-20
 */
@Service
public class SysMenuRoleServiceImpl extends ServiceImpl<SysMenuRoleMapper, SysMenuRole> implements SysMenuRoleService{
    @Override

    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SysMenuRole> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");

        IPage<SysMenuRole> page = this.page(
        new Query<SysMenuRole>().getPage(jsonObject),
        queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void create(SysMenuRole sysMenuRole) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(sysMenuRole);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void update(SysMenuRole sysMenuRole) {
        super.updateById(sysMenuRole);
    }


    @Override
    public SysMenuRole getById(String id) {
        return super.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void delete(String id) {
        super.removeById(id);
    }

     @Override
     @Transactional(rollbackFor = Exception.class)
     @NotResubmit
     public void deleteBatch(List<String> ids ) {
        super.removeByIds(ids);
        }


    @Override
    public void createByMenuId(List<String> menusIds ,String roleId) {
        ArrayList<SysMenuRole> sysMenuRoleList = new ArrayList<>();
        for (String menusId : menusIds) {
            SysMenuRole sysMenuRole = new SysMenuRole();
            sysMenuRole.setMenuId(menusId);
            sysMenuRole.setRoleId(roleId);
            sysMenuRoleList.add(sysMenuRole);
        }
        saveBatch(sysMenuRoleList);
    }


    @Override
    public void saveBatchByRoleId(String roleId, List<String> menusIds) {
        List<SysMenuRole> list = lambdaQuery().eq(SysMenuRole::getRoleId, roleId)
                .select(SysMenuRole::getSysMenuRoleId).list();
        if (list.size()>0){
            List<String> collect = list.stream().map(item -> item.getSysMenuRoleId()).collect(Collectors.toList());
            deleteBatch(collect);
        }
        if (menusIds!=null){
            createByMenuId(menusIds,roleId);
        }

    }

    @Override
    public List<String> getMenuAndSysRoleDateListById(String roleId) {
        List<SysMenuRole> list = lambdaQuery().eq(SysMenuRole::getRoleId, roleId).select(SysMenuRole::getMenuId).list();
        return list.stream()
                .map(SysMenuRole::getMenuId)
                .collect(Collectors.toList());
    }


    @Override
    public List<SysMenuRole> roleByMenuId(String menuId) {
       return  null;
    }
}
