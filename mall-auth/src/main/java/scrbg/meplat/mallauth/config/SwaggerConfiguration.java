package scrbg.meplat.mallauth.config;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.core.annotation.Order;
import springfox.bean.validators.configuration.BeanValidatorPluginsConfiguration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;

@Configuration
@EnableSwagger2WebMvc
@EnableKnife4j
@Import(BeanValidatorPluginsConfiguration.class)
public class SwaggerConfiguration {

    @Bean(value = "api")
    @Order(value = 1)
    public Docket groupRestApi1() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(groupApiInfo())
                .groupName("接口")
                .select()
                .apis(RequestHandlerSelectors.basePackage("scrbg.meplat.mallauth.controller"))
                .paths(PathSelectors.any())
                .build();
    }

    private ApiInfo groupApiInfo() {
        Contact contact = new Contact("hzb", null, "");
        return new ApiInfoBuilder()
                .title("")
                .description("<div style='font-size:14px;color:red;'>PCWP2.0-组织机构服务</div>")
                .termsOfServiceUrl("http://www.group.com/")
                .contact(contact)
                .version("1.0")
                .build();
    }

}

