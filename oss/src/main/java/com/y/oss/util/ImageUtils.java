package com.y.oss.util;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;

import org.springframework.http.MediaType;

public class ImageUtils {

    private static final Map<String, MediaType> FORMAT_MEDIA_TYPE_MAP = new HashMap<>();
    static {
        FORMAT_MEDIA_TYPE_MAP.put("jpeg", MediaType.IMAGE_JPEG);
        FORMAT_MEDIA_TYPE_MAP.put("jpg", MediaType.IMAGE_JPEG);
        FORMAT_MEDIA_TYPE_MAP.put("png", MediaType.IMAGE_PNG);
        FORMAT_MEDIA_TYPE_MAP.put("gif", MediaType.IMAGE_GIF);
        FORMAT_MEDIA_TYPE_MAP.put("pdf", MediaType.APPLICATION_PDF);
        FORMAT_MEDIA_TYPE_MAP.put("bmp", MediaType.valueOf("image/bmp"));
        FORMAT_MEDIA_TYPE_MAP.put("wbmp", MediaType.valueOf("image/vnd.wap.wbmp"));
    }

    /**
     * 从 InputStream 检测图片类型并返回对应 MediaType（支持 Thumbnails 的格式）
     */
    public static MediaType detectImageMediaType(byte[] imageBytes) {
        try {
            if (imageBytes[0] == 0x25 && imageBytes[1] == 0x50 && imageBytes[2] == 0x44 && imageBytes[3] == 0x46) {
            return FORMAT_MEDIA_TYPE_MAP.get("pdf");
        }

            // 判断是否是合法图片
            try (ByteArrayInputStream bais = new ByteArrayInputStream(imageBytes)) {
                BufferedImage image = ImageIO.read(bais);
                if (image == null) {
                    return null;
                }
            }

            // 再次读取格式名
            try (ByteArrayInputStream bais = new ByteArrayInputStream(imageBytes);
                 ImageInputStream imageInputStream = ImageIO.createImageInputStream(bais)) {

                Iterator<ImageReader> readers = ImageIO.getImageReaders(imageInputStream);
                if (readers.hasNext()) {
                    String format = readers.next().getFormatName().toLowerCase();
                    return FORMAT_MEDIA_TYPE_MAP.getOrDefault(format, null);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        return null;
    }
    


}
