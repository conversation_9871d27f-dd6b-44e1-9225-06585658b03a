package com.y.oss.util;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;

import net.coobird.thumbnailator.Thumbnails;

public class ThumbnailUtils {

    /**
     * 将 PDF 的第一页转为缩略图
     * @param pdfInput PDF 文件输入流
     * @param width 缩略图宽度
     * @param height 缩略图高度
     * @return 缩略图的字节数组（默认输出 JPEG）
     */
    public static byte[] pdfToThumbnail(byte[] pdfInput, int width, int height) throws IOException {
        try (PDDocument document = PDDocument.load(pdfInput)) {
            PDFRenderer renderer = new PDFRenderer(document);

            // 渲染第一页为图像（参数是页码，从0开始）
            BufferedImage pageImage = renderer.renderImageWithDPI(0, 150); // DPI 可调

            // 缩略图处理
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            Thumbnails.of(pageImage)
                    .size(width, height)
                    .outputFormat("png")
                    .toOutputStream(outputStream);

            return outputStream.toByteArray();
        }
    }

    /**
     * 将图片字节数组生成缩略图，返回缩略图的字节数组
     * @param inputBytes 原图 byte[]
     * @param width 缩略图宽度
     * @param height 缩略图高度
     * @param outputFormat 输出格式（如 "jpg", "png"）
     */
    public static byte[] createThumbnail(byte[] inputBytes, int width, int height) throws IOException {
        try (
            ByteArrayInputStream bais = new ByteArrayInputStream(inputBytes);
            ByteArrayOutputStream baos = new ByteArrayOutputStream()
        ) {
            Thumbnails.of(bais)
                    .size(width, height)
                    .outputFormat("png")
                    .toOutputStream(baos);
            return baos.toByteArray();
        }
    }
}

