package com.y.oss.config.minio;
import io.minio.MinioClient;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "minio")
public class MinioConfig {

    private String endpoint;
    private String accessKey;
    private String secretKey;
    private boolean cluster = false; // 添加集群标志

    @Bean
    public MinioClient getMinioClient() {
        if (cluster) {
            // 集群模式 - 使用分割后的多个端点
            String[] endpoints = endpoint.split(",");
            MinioClient.Builder builder = MinioClient.builder()
                    .credentials(accessKey, secretKey);

            // 添加每个端点
            for (String ep : endpoints) {
                builder.endpoint(ep.trim());
            }

            return builder.build();
        } else {
            // 单节点模式
            return MinioClient.builder()
                    .endpoint(endpoint)
                    .credentials(accessKey, secretKey)
                    .build();
        }

    }
}