package com.y.oss.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class MyMetaObjectHandler implements MetaObjectHandler {


    // 插入时的填充策略
    @Override
    public void insertFill(MetaObject metaObject) {
        this.setFieldValByName("gmtCreate",new Date(),metaObject);
        this.setFieldValByName("gmtModified",new Date(),metaObject);
//        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
//        if(currentUser != null){
//            this.setFieldValByName("founderName",currentUser.getUserName(),metaObject);
//            this.setFieldValByName("founderId",currentUser.getUserId(),metaObject);
//        }
        this.setFieldValByName("isDelete", 0,metaObject);
    }

    //更新时的填充策略
    @Override
    public void updateFill(MetaObject metaObject) {
        this.setFieldValByName("gmtModified",new Date(),metaObject);
    }
}
