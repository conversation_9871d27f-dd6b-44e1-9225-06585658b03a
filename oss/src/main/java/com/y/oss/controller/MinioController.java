package com.y.oss.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.scrbg.common.utils.R;
import com.y.oss.entity.FileRecord;
import com.y.oss.service.FileRecordService;
import com.y.oss.service.MinioService;

import io.minio.MinioClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;

@Log4j2
@RequestMapping("/oss")
@Api(tags = "文件上传")
@ApiSort(value = 1)
@RestController
public class MinioController {


    @Autowired
    private MinioClient minioClient;

    @Autowired
    private FileRecordService fileRecordService;

    @Autowired
    private MinioService minioService;

    /**
     * 上传文件
     *
     * @param files 本地文件
     * @return
     */
    @PostMapping("/uploader1")
    @ApiOperation(value = "上传多个文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bucketName", value = "桶名称", required = true, paramType = "String"),
            @ApiImplicitParam(name = "directory", value = "存储目录", required = true, paramType = "String"),
            @ApiImplicitParam(name = "files", value = "多个文件", required = true, paramType = "form"),
            @ApiImplicitParam(name = "isResetName", value = "是否使用原文件名（0否1是，默认否）", required = false, paramType = "Integer"),
            @ApiImplicitParam(name = "fileType", value = "文件类型（1图片2视频3文件）默认图片", required = false, paramType = "Integer"),
    })
    public R<List<FileRecord>> minioUpload(String bucketName, String directory, List<MultipartFile> files,Integer isResetName , Integer fileType) {
        HashMap<Object, Object> map = new HashMap<>();
        map.put("bucketName",bucketName);
        map.put("directory",directory);
        map.put("files",files);
        map.put("fileType",fileType);
        if(isResetName == null){
            map.put("isResetName",0);
        }else {
            map.put("isResetName",isResetName);
        }
        List<FileRecord> vos = minioService.minioUpload(map);
        return R.success(vos);
    }

    @PostMapping("/uploader2")
    @ApiOperation(value = "上传单个文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bucketName", value = "桶名称", required = true, paramType = "String"),
            @ApiImplicitParam(name = "directory", value = "存储目录", required = true, paramType = "String"),
            @ApiImplicitParam(name = "file", value = "文件", required = true, paramType = "form"),
            @ApiImplicitParam(name = "isResetName", value = "是否使用原文件名（0否1是，默认否）", required = false, paramType = "Integer"),
            @ApiImplicitParam(name = "fileType", value = "文件类型（1图片2视频3文件）默认图片", required = false, paramType = "Integer"),
    })
    public R<List<FileRecord>> minioUpload(String bucketName, String directory, MultipartFile file,Integer isResetName , Integer fileType) {
        ArrayList<MultipartFile> multipartFiles = new ArrayList<>();
        multipartFiles.add(file);
        HashMap<Object, Object> map = new HashMap<>();
        map.put("bucketName",bucketName);
        map.put("directory",directory);
        map.put("files",multipartFiles);
        map.put("fileType",fileType);
        if(isResetName == null){
            map.put("isResetName",0);
        }else {
            map.put("isResetName",isResetName);
        }
        List<FileRecord> vos = minioService.minioUpload(map);
        return R.success(vos);
    }

    @PostMapping("/materialUploader")
    @ApiOperation(value = "上传多个物资文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "files", value = "多个文件（数组）", required = true, paramType = "form"),
//            @ApiImplicitParam(name = "isResetName", value = "是否使用原文件名（0否1是，默认否）", required = false, paramType = "Integer"),
//            @ApiImplicitParam(name = "fileType", value = "文件类型（1图片2视频3文件）默认图片", required = false, paramType = "Integer"),
    })
    public R<List<FileRecord>> materialUploader(List<MultipartFile> files,Integer isResetName , Integer fileType) {
        HashMap<Object, Object> map = new HashMap<>();
        map.put("bucketName","mall");
        map.put("directory","material");
        map.put("files",files);
        map.put("fileType",fileType);
        if(isResetName == null){
            map.put("isResetName",0);
        }else {
            map.put("isResetName",isResetName);
        }
        List<FileRecord> vos = minioService.minioUpload(map);
        return R.success(vos);
    }

    @GetMapping("/downloader")
    @ApiOperation("文件下载")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "recordId", value = "记录id", required = true, paramType = "String"),
    })
    public Object downloadById(String recordId) {
        return minioService.downloadById(recordId);
    }

    @GetMapping("/deleteFile")
    @ApiOperation("删除文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "recordId", value = "记录id", required = true, paramType = "String"),
    })
    public R deleteFile(String recordId) {
        minioService.deleteFile(recordId);
        return R.success();
    }

    @GetMapping("/thumbnail")
    @ApiOperation("文件缩略图")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "recordId", value = "记录id", required = true, paramType = "String"),
    })
    public ResponseEntity<byte[]> thumbnail(String recordId) {
        byte[] imageBytes = minioService.thumbnail(recordId);
        return ResponseEntity.ok()
            .contentType(MediaType.IMAGE_PNG) // 生成的缩略图都是png格式的
            .body(imageBytes);
    }



}
