package com.y.oss.service;

import com.y.oss.entity.FileRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import com.y.oss.entity.FileRecord;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
/**
 * @描述：文件上传记录信息 服务类
 * @作者: ye
 * @日期: 2023-03-27
 */
public interface FileRecordService extends IService<FileRecord> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<FileRecord> queryWrapper);

        void create(FileRecord fileRecord);

        void update(FileRecord fileRecord);

        FileRecord getById(String id);

        void delete(String id);
}