package com.y.oss.service;

import java.util.List;
import java.util.Map;

import com.y.oss.entity.FileRecord;

/**
 * <AUTHOR>
 * @create 2023-03-27 17:05
 */
public interface MinioService {

    /**
     * 文件上传
     * @param map
     * @return
     */
    List<FileRecord> minioUpload(Map map);

    /**
     * 根据记录id下载
     * @param recordId
     * @return
     */
    Object downloadById(String recordId);

    /**
     * 删除文件
     * @param recordId
     */
    void deleteFile(String recordId);

    byte[] thumbnail(String recordId);

}
