package com.y.oss.service.impl;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StreamUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import com.y.oss.config.minio.FileContentType;
import com.y.oss.config.minio.ImageContentType;
import com.y.oss.config.minio.VideoContentType;
import com.y.oss.entity.FileRecord;
import com.y.oss.exception.BusinessException;
import com.y.oss.service.FileRecordService;
import com.y.oss.service.MinioService;
import com.y.oss.util.ImageUtils;
import com.y.oss.util.ThumbnailUtils;

import io.minio.BucketExistsArgs;
import io.minio.GetObjectArgs;
import io.minio.GetObjectResponse;
import io.minio.GetPresignedObjectUrlArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.RemoveObjectArgs;
import io.minio.errors.ErrorResponseException;
import io.minio.errors.InsufficientDataException;
import io.minio.errors.InternalException;
import io.minio.errors.InvalidResponseException;
import io.minio.errors.ServerException;
import io.minio.errors.XmlParserException;
import io.minio.http.Method;
import lombok.extern.log4j.Log4j2;

/**
 * <AUTHOR>
 * @create 2023-03-27 17:06
 */
@Log4j2
@Service
public class MinioServiceImpl implements MinioService {

    @Autowired
    private MinioClient minioClient;

    @Autowired
    private FileRecordService fileRecordService;

    /**
     * 文件上传
     *
     * 年月第几周/路径/文件类型/文件名
     * @param map
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<FileRecord> minioUpload(Map map) {
        String bucketName = (String) map.get("bucketName");
        String directory = (String) map.get("directory");
        Integer fileType = (Integer) map.get("fileType");
        Integer isResetName = (Integer) map.get("isResetName");
        List<MultipartFile> files = (List<MultipartFile>) map.get("files");
        if (fileType == null) fileType = 1;
        if (CollectionUtils.isEmpty(files) || files.get(0) == null || files.get(0).getSize() == 0) {
            throw new BusinessException(400, "文件为空！");
        }
        if (StringUtils.isEmpty(bucketName)) {
            throw new BusinessException(400, "桶名称不能为空！");
        }
        if (StringUtils.isEmpty(directory)) {
            throw new BusinessException(400, "存储目录不能为空！");
        }
        ArrayList<FileRecord> vos = new ArrayList<>();
        try {
            if (!minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build())) {
                throw new BusinessException(400, "桶不存在！请联系管理员！");
            }
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            String thisDateStr = LocalDate.now().format(formatter);
            for (MultipartFile file : files) {
                String fileName = file.getOriginalFilename();
                String contentType = null;
                String newName = null;
                String fileNewName = null;
                if(isResetName == 0){
                    fileNewName = UUID.randomUUID().toString().replaceAll("-", "")
                            + "-" + thisDateStr + fileName.substring(fileName.lastIndexOf("."));
                }else {
                    fileNewName = fileName;
                }
                if (fileType == 1) {
                    contentType = ImageContentType.getContentType(fileName);
                    if (contentType == null) {
                        throw new BusinessException(400, "不支持该类型文件上传！");
                    }
                    newName = getThisWeek() + "/" + directory  + "/image/" + thisDateStr + "/" + fileNewName;
//                    newName =  directory  + "/image/" + thisDateStr + "/" + fileNewName;
                } else if (fileType == 2) {
                    contentType = VideoContentType.getContentType(fileName);
                    if (contentType == null) {
                        throw new BusinessException(400, "不支持该类型文件上传！");
                    }
                    newName = getThisWeek() + "/" + directory  + "/video/" + thisDateStr + "/" + fileNewName;
//                    newName = directory  + "/video/" + thisDateStr + "/" + fileNewName;
                }else if(fileType == 3){
                    contentType = FileContentType.getContentType(fileName);
                    if (contentType == null) {
                        // 可以支持图片
                        contentType = ImageContentType.getContentType(fileName);
                        if(contentType == null) {
                            // 可以支持视频
                            contentType = VideoContentType.getContentType(fileName);
                            if(contentType == null){
                                throw new BusinessException(400, "不支持该类型文件上传！");
                            }
                        }
                    }
                    newName = getThisWeek() + "/" + directory +  "/file/" + thisDateStr + "/" + fileNewName;
//                    newName = directory +  "/file/" + thisDateStr + "/" + fileNewName;
                }else {
                    throw new BusinessException(400, "请输入正确的文件类型");
                }
                InputStream inputStream = file.getInputStream();
                // 设置文件contentType，不设置默认url访问返回流会直接下载
                PutObjectArgs build = PutObjectArgs.builder()
                        .bucket(bucketName)
                        .object(newName)
                        .stream(inputStream, file.getSize(), -1)
                        .contentType(contentType)
                        .build();
                minioClient.putObject(build);
                inputStream.close();
                String url = minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                        .method(Method.GET)
                        .bucket(bucketName)
                        .object(newName)
                        .build());
                // 需要建表
                if (!StringUtils.isEmpty(url)) {
                    String[] split = url.split("\\?");
                    url = split[0];
                }
                FileRecord vo = new FileRecord();
                vo.setObjectName(fileNewName);
                vo.setObjectPath(url);
                vo.setNonIpObjectPath("/" + bucketName + "/" + newName);
                vo.setBucketName(bucketName);
                BigDecimal bigDecimal = new BigDecimal(file.getSize());
                BigDecimal divideMB = bigDecimal.divide(new BigDecimal(1024)).divide(new BigDecimal(1024));
                BigDecimal divideKB = bigDecimal.divide(new BigDecimal(1024));
                vo.setObjectSizeKb(divideKB.setScale(2, BigDecimal.ROUND_DOWN));
                vo.setObjectSizeMb(divideMB.setScale(2, BigDecimal.ROUND_DOWN));
                boolean save = fileRecordService.save(vo);
                if (save) {
                    vos.add(vo);
                }
            }
            return vos;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(400, e.getMessage());
        }
    }

    public static String getThisWeek() {
        LocalDate today = LocalDate.now();
        String str = today.toString().substring(0,today.toString().lastIndexOf("-") + 1);
        WeekFields weekFields = WeekFields.of(Locale.getDefault());
        int weekNumber = today.get(weekFields.weekOfMonth());
        String strWeek = "oss-url-" + str + weekNumber + "week";
        return strWeek;
    }

    /**
     * 根据记录id下载
     * @param recordId
     * @return
     */
    @Override
    public Object downloadById(String recordId) {
            if (StringUtils.isEmpty(recordId)) {
            throw new BusinessException(400, "未携带记录id！");
        }
        FileRecord fileRecord = fileRecordService.lambdaQuery().eq(FileRecord::getRecordId, recordId)
                .select(FileRecord::getBucketName, FileRecord::getNonIpObjectPath).one();
        if (fileRecord == null) {
            throw new BusinessException(400, "文件记录不存在！");
        }
        GetObjectResponse response = null;
        try {
            if(fileRecord.getNonIpObjectPath().indexOf(fileRecord.getBucketName()) != -1){
                String nonIpObjectPath = fileRecord.getNonIpObjectPath();
                String replace = nonIpObjectPath.replace("/" + fileRecord.getBucketName(), "");
                response = minioClient.getObject(GetObjectArgs.builder().bucket(fileRecord.getBucketName()).object(replace).build());
            }else {
                response = minioClient.getObject(GetObjectArgs.builder().bucket(fileRecord.getBucketName()).object(fileRecord.getNonIpObjectPath()).build());
            }
        } catch (Exception e) {
            log.error("异常信息：" + e.getMessage());
            throw new BusinessException(400, "下载失败！");
        }
        InputStreamResource resource = new InputStreamResource(response);
        return resource;
    }

    /**
     * 删除文件
     * @param recordId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFile(String recordId) {
        if (StringUtils.isEmpty(recordId)) {
            throw new BusinessException(400, "未携带记录id！");
        }
        FileRecord fileRecord = fileRecordService.lambdaQuery().eq(FileRecord::getRecordId, recordId)
                .select(FileRecord::getBucketName, FileRecord::getNonIpObjectPath).one();
        if (fileRecord == null) {
            throw new BusinessException(400, "文件记录不存在！");
        }
        try {
            // TODO 这里考虑删除缩略图
            if(fileRecord.getNonIpObjectPath().indexOf(fileRecord.getBucketName()) != -1){
                String nonIpObjectPath = fileRecord.getNonIpObjectPath();
                String replace = nonIpObjectPath.replace("/" + fileRecord.getBucketName(), "");
                minioClient.removeObject(RemoveObjectArgs.builder().bucket(fileRecord.getBucketName()).object(replace).build());
            }else {
                minioClient.removeObject(RemoveObjectArgs.builder().bucket(fileRecord.getBucketName()).object(fileRecord.getBucketName()).build());
            }
            fileRecordService.delete(recordId);
        } catch (Exception e) {
            log.error("异常信息：" + e.getMessage());
            throw new BusinessException(400, "删除失败！");
        }
    }

    @Override
    public byte[] thumbnail(String recordId) {
        if (StringUtils.isEmpty(recordId)) {
            throw new BusinessException(400, "未携带记录id！");
        }
        FileRecord fileRecord = fileRecordService.lambdaQuery().eq(FileRecord::getRecordId, recordId)
                .select(FileRecord::getBucketName, FileRecord::getNonIpObjectPath).one();
        if (fileRecord == null) {
            throw new BusinessException(400, "文件记录不存在！");
        }
        GetObjectResponse response = null;
        String name = fileRecord.getNonIpObjectPath();
        String bucketName = fileRecord.getBucketName();
        // 似乎是历史原因，这里需要这样预处理
        if(name.indexOf(bucketName) != -1){
            name = name.replace("/" + bucketName, "");
        }
        // 原图片与其缩略图的name关系如下
        String thumbnailName = addThumbnailSuffix(name);
        try {
            response = GetObjectResponse(bucketName, thumbnailName);
        }  catch (ErrorResponseException e) {
            // 没找到，是首次请求，尝试生成缩略图
            // 这里就不双重检查了，出现问题也可以容忍
            if ("NoSuchKey".equals(e.errorResponse().code())) {
                // 请求原图
                try {
                    response = GetObjectResponse(bucketName, name);
                } catch (Exception e1) {
                    log.error("请求文件失败：" , e1);
                    throw new BusinessException(500, e1.getMessage());
                }
                // 原图转换成bytes
                byte[] originBytes;
                try {
                    originBytes =  StreamUtils.copyToByteArray(response);
                } catch (IOException e1) {
                    log.error("" , e1);
                    throw new BusinessException(500, e.getMessage());
                }
                // 根据文件内容推断文件类型，只支持一些图片类型和pdf
                // 这里需要推断是因为原始数据没有存储文件类型
                MediaType mediaType = ImageUtils.detectImageMediaType(originBytes);
                if (mediaType==null) {
                    throw new BusinessException(500, "该文件不支持缩略图展示");
                }
                // 生成缩略图，pdf需要特殊处理
                byte[] thumbnailBytes;
                try {
                        
                    if (mediaType==MediaType.APPLICATION_PDF) {
                        thumbnailBytes = ThumbnailUtils.pdfToThumbnail(originBytes, 200, 200);
                    }else {
                        thumbnailBytes = ThumbnailUtils.createThumbnail(originBytes, 200, 200);

                    }
                } catch (Exception e1) {
                    log.error("", e1);
                    throw new BusinessException(500, "生成缩略图失败");
                }
                // 生成缩略图后，保存至minio
                try (ByteArrayInputStream bais = new ByteArrayInputStream(thumbnailBytes)) {
                    minioClient.putObject(
                        PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(thumbnailName)
                            .stream(bais, thumbnailBytes.length, -1)
                            .contentType(mediaType.toString())
                            .build()
                    );
                } catch (Exception e1) {
                    log.warn("", e1);
                    throw new BusinessException(500, "生成缩略图失败");
                }
                return thumbnailBytes;
            }else {
                log.error("请求文件失败：" , e);
                throw new BusinessException(500, e.getMessage());
            }
        } catch (Exception e) {
            log.error("请求文件失败：" , e);
            throw new BusinessException(500, e.getMessage());
        }
        // 找到缩略图，直接返回
        try {
            return StreamUtils.copyToByteArray(response);
        } catch (IOException e) {
            log.error("" , e);
            throw new BusinessException(500, e.getMessage());
        }

    }

    private GetObjectResponse GetObjectResponse(String bucketName, String objectName) throws InvalidKeyException, ErrorResponseException, InsufficientDataException, InternalException, InvalidResponseException, NoSuchAlgorithmException, ServerException, XmlParserException, IllegalArgumentException, IOException {
        return minioClient.getObject(GetObjectArgs.builder().bucket(bucketName).object(objectName).build());
    }

    /**
     * 在路径中最后一个扩展名前插入 _thumbnail
     * 如果没有扩展名，则直接在末尾拼接_thumbnail
     * @param originalPath 原始路径
     * @return 修改后的路径
     */
    public static String addThumbnailSuffix(String originalPath) {
        int lastDotIndex = originalPath.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return originalPath + "_thumbnail";
        }

        String prefix = originalPath.substring(0, lastDotIndex);
        String extension = originalPath.substring(lastDotIndex); // 包含点
        return prefix + "_thumbnail" + extension;
    }

    


}
