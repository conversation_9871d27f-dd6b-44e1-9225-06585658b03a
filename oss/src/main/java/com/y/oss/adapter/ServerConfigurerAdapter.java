package com.y.oss.adapter;

import com.scrbg.common.utils.UserInterceptor;
import com.y.oss.interceptor.CheckTokenInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class ServerConfigurerAdapter implements WebMvcConfigurer {

    @Autowired
    private CheckTokenInterceptor checkTokenInterceptor;
    @Bean
    public HandlerInterceptor userInterceptor() {
        return new UserInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(userInterceptor());
        // 拦截
        registry.addInterceptor(checkTokenInterceptor)
                .addPathPatterns("/**")
//                .excludePathPatterns("/**")
                // 排除
                .excludePathPatterns("/doc.html","/webjars/**","/swagger-resources","/error","/favicon.ico");
    }
}
