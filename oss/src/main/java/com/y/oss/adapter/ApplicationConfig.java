package com.y.oss.adapter;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodReturnValueHandler;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

@Configuration
//@ComponentScan(basePackages = {"com.scrbg"}, useDefaultFilters = true)
public class ApplicationConfig implements WebMvcConfigurer {

    @Bean
    public JsonReturnHandler jsonReturnHandler() {
        return new JsonReturnHandler();//初始化json过滤器
    }

    @Override
    public void addReturnValueHandlers(List<HandlerMethodReturnValueHandler> handlers) {
        handlers.add(jsonReturnHandler());
        //WebMvcConfigurer.super.addReturnValueHandlers(handlers);
    }
}
