server:
  port: 10010
spring:
  application:
    name: gateway
  cloud:
#    nacos:
#      discovery:
#        server-addr: ************:9848
#        #ip: ************
    gateway: # TODO 注意这里没有oss相关的配置，前端直连oss微服务，且不需要认证 当前配置应该有冗余，后续要删除
      routes:
        - id: mall-material-id
          uri: lb://mall-material
          predicates:
            - Path=/materialMall/**
          filters:
            - StripPrefix=1 # 去除前缀
        - id: mall-device-id
          uri: lb://mall-device
          predicates:
            - Path=/deviceMall/**
          filters:
            - StripPrefix=1 # 去除前缀
        - id: mall-supplier-id
          uri: lb://mall-supplier
          predicates:
            - Path=/supplier/**
          filters:
            - StripPrefix=1 # 去除前缀
        - id: mall-tender-id
          uri: lb://mall-tender
          predicates:
            - Path=/tender/**
          filters:
            - StripPrefix=1 # 去除前缀
  profiles:
    active: dev
secure:
  ignore:
    urls: #配置白名单路径
      - "/*/doc.html"
      - "/*/swagger-resources/**"
      - "/*/swagger/**"
      - "/*/outer/usedDevice/**"
      - "/*/w/**"
      - "/*/webjars/**"
      - "/*/actuator/**"
      - "/*/favicon.ico"
      - "/*/error"
      - "/*/v2/api-docs"
seata:
  enabled: false

#  nacos密码： nacos  SL@sl1#lT9@19
