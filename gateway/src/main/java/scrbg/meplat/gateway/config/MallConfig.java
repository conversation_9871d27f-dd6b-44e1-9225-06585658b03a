package scrbg.meplat.gateway.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MallConfig {


    @Value("${mall.loginOutTime}")
    public Integer loginOutTime;


    @Value("${mall.prodPcwp2Url}")
    public String prodPcwp2Url;

    @Value("${mall.prodPcwp2Url02}")
    public String prodPcwp2Url02;

}
