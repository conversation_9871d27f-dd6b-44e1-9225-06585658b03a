package scrbg.meplat.gateway.config.restTemplateConfig;


import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @create 2022-12-21 15:49
 */
@Configuration
public class RestTemplateConfig {
    @Bean
    @Resource(name="simpleClientHttpRequestFactory") // 在多ClientHttpRequestFactory的时候指定用哪个
    public RestTemplate restTemplate(ClientHttpRequestFactory factory) {
        return new RestTemplate(factory);
    }

    @Bean
    public ClientHttpRequestFactory simpleClientHttpRequestFactory() {
        SSL factory = new SSL();
        factory.setReadTimeout(60000);
        factory.setConnectTimeout(30000);//单位为ms
        return factory;
    }

}