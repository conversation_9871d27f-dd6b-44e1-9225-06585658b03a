-- =======================================================================================
-- 执行日期: 2025-05-17
-- 修改人: [谭飞]
-- 功能说明: 为平台交易费缴费记录表添加结算周期相关字段
-- =======================================================================================
ALTER TABLE `platform_deal_fee_record`
ADD COLUMN `settle_date` date DEFAULT NULL COMMENT '结算日期' AFTER `state`,
ADD COLUMN `period_start_date` date DEFAULT NULL COMMENT '当期结算交易开始日期' AFTER `settle_date`,
ADD COLUMN `period_end_date` date DEFAULT NULL COMMENT '当期结算交易结束日期' AFTER `period_start_date`,
ADD COLUMN `payment_deadline` date DEFAULT NULL COMMENT '缴费截止日期' AFTER `period_end_date`,
ADD COLUMN `period_transaction_amount` decimal(18,2)  COMMENT '本次结算交易额' AFTER `payment_deadline`,
ADD COLUMN `total_transaction_amount` decimal(18,2) COMMENT '累计结算交易额' AFTER `period_transaction_amount`,
ADD COLUMN `auditor_id` char(36)  DEFAULT NULL COMMENT '审核人员ID' AFTER `state`,
ADD COLUMN `auditor_name` varchar(50)  DEFAULT NULL COMMENT '审核人员姓名' AFTER `auditor_id`;
-- =======================================================================================
-- 执行日期: 2025-05-17
-- 修改人: [谭飞]
-- 功能说明: platform_year_fee_record年费缴费记录新添加有效期开始日期、有效期截止日期
-- =======================================================================================
ALTER TABLE `platform_year_fee_record`
ADD COLUMN `serve_start_time` date DEFAULT NULL COMMENT '有效期开始日期' AFTER `payment_duration_type`,
ADD COLUMN `serve_end_time` date DEFAULT NULL COMMENT '有效期截止日期' AFTER `serve_start_time`;
-- =======================================================================================
-- 执行日期: 2025-05-19
-- 修改人: [谭飞]
-- 功能说明: shopping_cart购物车新增账期，账期可调整
-- =======================================================================================
ALTER TABLE `shopping_cart`
ADD COLUMN `payment_period` tinyint COMMENT '账期（月数：1、2、3个月）'
AFTER `tax_rate`;
-- =======================================================================================
-- 执行日期: 2025-05-20
-- 修改人: [谭飞]
-- 功能说明: platform_deal_fee_record表state状态（0待确认1确认中2确认成功3确认失败4审核中5审核通过6审核未通过）
-- 功能说明: platform_deal_fee_record表fee_ratio 交易服务费率收取比例（%）
-- =======================================================================================
ALTER TABLE `platform_deal_fee_record`
MODIFY COLUMN `state` tinyint NOT NULL DEFAULT '0'
COMMENT '状态（0待确认1确认中2确认成功3确认失败4审核中5审核通过6审核未通过）';
ALTER TABLE `platform_deal_fee_record`
ADD COLUMN `fee_ratio` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '收取比例（%）'
AFTER `pay_type`;
-- 执行日期: 2025-05-21
-- 修改人: [谭飞]
-- 功能说明: product，购物车展示是一口价还是参考价
-- =======================================================================================
ALTER TABLE `product`
ADD COLUMN `price_type` tinyint NOT NULL DEFAULT '0'
COMMENT '价格类型：0 一口价 1 参考价'
AFTER `product_type`;
-- =======================================================================================
-- 执行日期: 2025-05-22
-- 修改人: [陶醉]
-- 功能说明: product_comment商品评价新增评分分数-保供能力、评分分数-诚信履约、评分分数-服务水平，comment_level注释改为评分分数-商品品质
-- =======================================================================================
ALTER TABLE `product_comment`
ADD COLUMN `comment_supply` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '评价分数-保供能力' AFTER `comment_level`,
ADD COLUMN `comment_integrity` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '评价分数-诚信履约' AFTER `comment_supply`,
ADD COLUMN `comment_service` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '评价分数-服务水平' AFTER `comment_integrity`,
MODIFY COLUMN `comment_level` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '评价分数-商品品质';
-- =======================================================================================
-- 执行日期: 2025-05-22
-- 修改人: [陶醉]
-- 功能说明: 新增店铺评价表shop_comment
-- =======================================================================================
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
CREATE TABLE `shop_comment`  (
  `shop_comment_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '商铺评价关联表id',
  `shop_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '店铺id',
  `comment_service_score` decimal(2, 1) NULL DEFAULT NULL COMMENT '评价分数-服务评分',
  `comment_level` decimal(2, 1) UNSIGNED NULL DEFAULT NULL COMMENT '评价分数-商品品质',
  `comment_supply` decimal(2, 1) UNSIGNED NULL DEFAULT NULL COMMENT '评价分数-保供能力',
  `comment_integrity` decimal(2, 1) UNSIGNED NULL DEFAULT NULL COMMENT '评价分数-诚信履约',
  `comment_service` decimal(2, 1) UNSIGNED NULL DEFAULT NULL COMMENT '评价分数-服务水平',
  `month` char(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '统计月份（格式：YYYY-MM）',
  `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除： -1: 删除 0:未删除',
  `sort` int NULL DEFAULT NULL COMMENT '排序',
  `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
  `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
  `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  PRIMARY KEY (`shop_comment_id`) USING BTREE,
  INDEX `idx_comment_filter`(`shop_id` ASC, `is_delete` ASC, `comment_service_score` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '商铺评价' ROW_FORMAT = Dynamic;
SET FOREIGN_KEY_CHECKS = 1;
-- =======================================================================================
-- 执行日期: 2025-05-28
-- 修改人: [谭飞]
-- 功能说明: 新增计划主表plan
-- =======================================================================================
DROP TABLE IF EXISTS `plan`;
CREATE TABLE `plan` (
  `bill_id` varchar(64) NOT NULL COMMENT '计划ID',
  `bill_no` varchar(64) NOT NULL COMMENT '计划编号',
  `p_bill_id` varchar(64)  COMMENT 'PCWP计划ID',
  `p_bill_no` varchar(64) COMMENT 'PCWP计划编号',
  `state` varchar(2) NOT NULL DEFAULT '1' COMMENT '计划状态(1待审核2已审核)',
  `bill_date` datetime NOT NULL COMMENT '计划日期',
  `type` int(2) DEFAULT '0' COMMENT '计划类型(0零星采购 1大宗临购 2周转材料)',
  `org_id` varchar(64) NOT NULL COMMENT '采购人员所属组织机构ID',
  `org_name` varchar(100) NOT NULL COMMENT '采购人员所属组织机构名称',
  `org_short` varchar(50) COMMENT '采购人员所属组织机构简称',
  `plan_amount` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '计划金额(不含税)',
  `tax_plan_amount` decimal(18,2) DEFAULT '0.00' COMMENT '计划金额（含税）',
  `tax_amount` decimal(18,2) NOT NULL COMMENT '税额',
  `remark` varchar(500) COMMENT '备注',
  `founder_id` varchar(64) NOT NULL COMMENT '创建人ID',
  `founder_name` varchar(50) NOT NULL COMMENT '创建人名称',
  `modify_id` varchar(64) COMMENT '修改人ID',
  `modify_name` varchar(50) COMMENT '修改人名称',
  `gmt_create` datetime NOT NULL COMMENT '创建时间',
  `gmt_modified` datetime COMMENT '更新时间',
  `is_delete` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除(0未删除 1已删除)',
  `version` int COMMENT '乐观锁版本号',
   PRIMARY KEY (`bill_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='计划主表';
-- =======================================================================================
-- 执行日期: 2025-05-27
-- 修改人: [谭飞]
-- 功能说明: 新增计划明细表plan_detail
-- =======================================================================================
DROP TABLE IF EXISTS `plan_detail`;
CREATE TABLE `plan_detail` (
  `dtl_id` varchar(64) NOT NULL COMMENT '计划明细ID',
  `bill_id` varchar(64) NOT NULL COMMENT '计划ID',
  `p_dtl_id` varchar(64)  COMMENT 'PCWP计划明细ID',
  `material_id` varchar(64) NOT NULL COMMENT '物料ID',
  `material_name` varchar(100) NOT NULL COMMENT '物料名称',
  `spec` varchar(50) NOT NULL COMMENT '规格',
  `texture` varchar(100) NOT NULL COMMENT '材质',
  `unit` varchar(20) NOT NULL COMMENT '单位',
  `material_use` varchar(200) COMMENT '物料用途',
  `material_class_id` varchar(64) NOT NULL  COMMENT '物料分类ID',
  `material_class_name` varchar(100) NOT NULL  COMMENT '物料分类名称',
  `top_class_id` varchar(64) NOT NULL  COMMENT '顶级分类ID',
  `top_class_name` varchar(100) NOT NULL  COMMENT '顶级分类名称',
  `trade_id` varchar(64) NOT NULL COMMENT '商品ID',
  `trade_name` varchar(100) NOT NULL COMMENT '商品名称',
  `quantity` int NOT NULL DEFAULT '0.0000' COMMENT '数量',
  `price` decimal(18,2) NOT NULL COMMENT '单价(不含税)',
  `tax_price` decimal(18,2) NOT NULL COMMENT '含税单价',
  `amount` decimal(18,2) NOT NULL  COMMENT '金额',
  `tax_amount` decimal(18,2) NOT NULL COMMENT '税额',
  `consume_amount` decimal(18,2) NOT NULL COMMENT '已消耗金额',
  `not_consume_amount` decimal(18,2) NOT NULL COMMENT '未消耗金额',
  `consume_number` int NOT NULL COMMENT '已消耗数量',
  `not_consume_number` int NOT NULL COMMENT '未消耗数量',
  `shop_id` varchar(64) NOT NULL COMMENT '店铺ID',
  `shop_name` varchar(100)  NOT NULL COMMENT '店铺名称',
  `storage_id` varchar(64) NOT NULL COMMENT '供应商ID',
  `storage_name` varchar(100)  NOT NULL COMMENT '供应商名称',
  `org_short` varchar(50) COMMENT '供应商机构简码',
  `storage_org_id` varchar(64) COMMENT '供应商组织机构ID(PCWP组织机构id)',
  `credit_code` varchar(50) NOT NULL COMMENT '统一社会信用代码',
  `founder_id` varchar(64) NOT NULL COMMENT '创建人ID',
  `founder_name` varchar(50) NOT NULL COMMENT '创建人名称',
  `modify_id` varchar(64)  COMMENT '修改人ID',
  `modify_name` varchar(50) COMMENT '修改人名称',
  `gmt_create` datetime NOT NULL COMMENT '创建时间',
  `gmt_modified` datetime COMMENT '更新时间',
  `is_delete` tinyint DEFAULT '0' COMMENT '逻辑删除(0未删除 1已删除)',
  `version` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
   PRIMARY KEY (`dtl_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='计划明细表';
-- =======================================================================================
-- 执行日期: 2025-05-27
-- 修改人: [喻清太]
-- 功能说明: product商品均价
-- =======================================================================================
ALTER TABLE `product`
ADD COLUMN `product_ave_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '商品均价' AFTER `tax_rate`;
-- =======================================================================================
-- 执行日期: 2025-06-09
-- 修改人: [谭飞]
-- 功能说明: supplier_reconciliation二级供应商物资验收，添加PCWP推送状态字段和关联字段
-- =======================================================================================
ALTER TABLE supplier_reconciliation
ADD COLUMN `is_not_push` tinyint DEFAULT NULL COMMENT '是否已推送PCWP（0待推送1已推送null不需要推送）',
ADD COLUMN `relevance_id` char(36) COMMENT '关联id（对应pcwp验收单id）',
ADD COLUMN `relevance_sn` varchar(50) COMMENT '关联名称（对应pcwp验收单编号）';
-- =======================================================================================
-- 执行日期: 2025-06-09
-- 修改人: [陶醉]
-- 功能说明: shop_comment店铺评价
-- =======================================================================================
ALTER TABLE `shop_comment`
ADD COLUMN `shop_name` varchar(50) COMMENT '店铺名称' AFTER `shop_id`,
ADD COLUMN `main_business` longtext  COMMENT '主营业务' AFTER `shop_name`,
ADD COLUMN `comment_start` datetime NULL DEFAULT NULL COMMENT '评价区间开始时间' AFTER `remarks`,
ADD COLUMN `comment_end` datetime NULL DEFAULT NULL COMMENT '评价区间结束时间' AFTER `comment_start`;
-- =======================================================================================
-- 执行日期: 2025-06-10
-- 修改人: [曹然]
-- 功能说明: 新建大宗清单附件表
-- =======================================================================================
CREATE TABLE `st_attachment` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `st_id` varchar(64) NOT NULL COMMENT '关联的清单 ID',
  `name` varchar(255) NOT NULL COMMENT '文件名称',
  `file_size` int NOT NULL COMMENT '文件大小（单位：字节）',
  `file_far_id` varchar(255) NOT NULL COMMENT '文件存储ID',
  `file_type` int NOT NULL COMMENT '1 图片，3 pdf',
  `upload_date` date NOT NULL COMMENT '上传日期',
  `sort` int DEFAULT NULL COMMENT '排序',
  `mall_type` int NOT NULL COMMENT '商城类型',
  `gmt_create` datetime NOT NULL COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL COMMENT '更新时间',
  `founder_name` varchar(64) NOT NULL COMMENT '创建人名称',
  `founder_id` varchar(64) NOT NULL COMMENT '创建人ID',
  `modify_name` varchar(64) DEFAULT NULL COMMENT '修改人名称',
  `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人ID',
  `remarks` varchar(512) DEFAULT NULL COMMENT '备注',
  `is_delete` tinyint DEFAULT '0' COMMENT '逻辑删除 -1: 删除 0:未删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='大宗清单附件表';
-- =======================================================================================
-- 执行日期: 2025-06-11
-- 修改人: [刘伟东]
-- 功能说明: 修改店铺供方关联表
-- =======================================================================================
ALTER TABLE `shop_supplier_rele`
ADD COLUMN `supplier_type` varchar(64)  NULL DEFAULT NULL COMMENT '供应商类型' AFTER `supplier_name`,
ADD COLUMN `template_warehoused_is` bit(1) NULL DEFAULT NULL COMMENT '是否采用定制化模式入库  1-是 0-否' AFTER `supplier_type`,
ADD COLUMN `warehoused_type` varchar(255) NULL DEFAULT NULL COMMENT '入库类型' AFTER `template_warehoused_is`,
ADD COLUMN `warehoused_desc` varchar(255) NULL DEFAULT NULL COMMENT '入库审批内容简述' AFTER `warehoused_type`,
ADD COLUMN `warehoused_file` varchar(64) NULL DEFAULT NULL COMMENT '证明材料' AFTER `warehoused_desc`,
ADD COLUMN `supplier_group` varchar(64) NULL DEFAULT NULL COMMENT '供应商群组' AFTER `warehoused_file`;
-- =======================================================================================
-- 执行日期: 2025-06-12
-- 修改人: [曹然]
-- 功能说明: 新建计划附件表
-- =======================================================================================
CREATE TABLE `plan_attachment` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `plan_id` varchar(64) NOT NULL COMMENT '关联的清单 ID',
  `name` varchar(255) NOT NULL COMMENT '文件名称',
  `file_size` int NOT NULL COMMENT '文件大小（单位：字节）',
  `file_far_id` varchar(255) NOT NULL COMMENT '文件存储ID',
  `upload_date` date NOT NULL COMMENT '上传日期',
  `sort` int DEFAULT NULL COMMENT '排序',
  `mall_type` int NOT NULL COMMENT '商城类型',
  `gmt_create` datetime NOT NULL COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL COMMENT '更新时间',
  `founder_name` varchar(64) NOT NULL COMMENT '创建人名称',
  `founder_id` varchar(64) NOT NULL COMMENT '创建人ID',
  `modify_name` varchar(64) DEFAULT NULL COMMENT '修改人名称',
  `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人ID',
  `remarks` varchar(512) DEFAULT NULL COMMENT '备注',
  `is_delete` tinyint DEFAULT '0' COMMENT '逻辑删除 -1: 删除 0:未删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='计划附件表';
-- =======================================================================================
-- 执行日期: 2025-06-12
-- 修改人: [曹然]
-- 功能说明: 大宗临购清单表变更问清单表同时存储大宗临购和周转材料清单
-- =======================================================================================
ALTER TABLE synthesize_temporary ADD stType INT DEFAULT 0 NOT NULL COMMENT '清单类型 0 大宗临购 1 周转材料 默认值0';
-- =======================================================================================
-- 执行日期: 2025-06-13
-- 修改人: [张凡军]
-- 功能说明: 发货单
-- =======================================================================================
ALTER TABLE `order_ship`
    ADD COLUMN `receive_status` TINYINT NULL DEFAULT NULL COMMENT 'PCWP现场收料状态' AFTER `out_key_id`,
    ADD COLUMN `receive_no` VARCHAR(255) NULL DEFAULT NULL COMMENT '收料编号' AFTER `receive_status`,
	ADD COLUMN `receive_audit_name` VARCHAR(255) NULL DEFAULT NULL COMMENT '收料审核人' AFTER `receive_no`,
	ADD COLUMN `receive_audit_is` TINYINT NULL DEFAULT NULL COMMENT '是否已审核' AFTER `receive_audit_name`;
-- =======================================================================================
-- 执行日期: 2025-06-13
-- 修改人: [谭飞]
-- 功能说明: 二级供应商对账单
-- 为supplier_reconciliation表新增已结算金额字段
ALTER TABLE `supplier_reconciliation`
ADD COLUMN `settle_amount` decimal(18,2) DEFAULT 0.00 COMMENT '已结算金额' AFTER `rate_amount`
-- =======================================================================================
-- 执行日期: 2025-06-13
-- 修改人: [陶醉]
-- 功能说明: 订单评价
-- 为orders表新增是否评价字段
ALTER TABLE `orders`
ADD COLUMN `is_comment` tinyint NOT NULL DEFAULT '0' COMMENT '评论状态：0 未评价 1 已评价' AFTER `out_key_id`;
-- =======================================================================================
-- 执行日期: 2025-06-13
-- 修改人: [张凡军]
-- 功能说明: 发货单
-- 为发货单表新增运输联系人、联系电话、签收人字段
-- =======================================================================================
ALTER TABLE `order_ship`
    ADD COLUMN `contact_user` VARCHAR(255) NULL DEFAULT NULL COMMENT '运输联系人' AFTER `receive_audit_is`,
    ADD COLUMN `contact_phone` CHAR(20) NULL DEFAULT NULL COMMENT '联系电话' AFTER `contact_user`,
    ADD COLUMN `recipient` VARCHAR(255) NULL DEFAULT NULL COMMENT '签收人' AFTER `contact_phone`;
-- =======================================================================================
-- 执行日期: 2025-06-17
-- 修改人: [刘伟东]
-- 功能说明: 店铺供方关联表
-- 新增审核状态、周转材料权限实体 修改群组字段的类型
-- =======================================================================================
ALTER TABLE `shop_supplier_rele`
    ADD COLUMN `permissions_turnover` VARCHAR(255) NULL DEFAULT NULL COMMENT '周转材料(0无权限，1有权限)' AFTER `permissions_low_value`,
    ADD COLUMN `audit_status` int NULL DEFAULT NULL COMMENT '审核状态' AFTER `supplier_group`,
    MODIFY COLUMN `supplier_group` text NULL DEFAULT NULL COMMENT '供应商群组';
-- =======================================================================================
-- 执行日期: 2025-06-19
-- 修改人: [谭飞]
-- 功能说明: 订单表关联计划表
-- 新增计划id和计划编号字段
-- =======================================================================================
ALTER TABLE `orders`
ADD COLUMN `plan_id` VARCHAR(64)  NULL COMMENT '计划id' AFTER `order_sn`,
ADD COLUMN `plan_no` VARCHAR(64)  NULL COMMENT '计划编号' AFTER `plan_id`;
-- =======================================================================================
-- 执行日期: 2025-06-19
-- 修改人: [刘伟东]
-- 功能说明: 商品上下架记录表
-- =======================================================================================
CREATE TABLE `product_shelf_log`  (
  `id` varchar(64)  NOT NULL COMMENT '记录id',
  `product_id` varchar(64) NULL DEFAULT NULL COMMENT '商品id',
  `product_name` varchar(64)  NULL DEFAULT NULL COMMENT '商品名称',
  `product_type` tinyint(255) NULL DEFAULT NULL COMMENT '商品类型：0 低值易耗品 1大宗临购 2、周转材料',
  `shop_id` varchar(64)  NULL DEFAULT NULL COMMENT '店铺id',
  `operation_type` tinyint(255) NULL DEFAULT NULL COMMENT '操作类型：0删除 1上架 2下架',
  `operation_time` datetime NULL DEFAULT NULL COMMENT '操作时间',
  `stock` decimal(18, 2) NULL DEFAULT NULL COMMENT '库存',
  `sort` int NULL DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic COMMENT='商品上下架记录表';

SET FOREIGN_KEY_CHECKS = 1;
-- =======================================================================================
-- 执行日期: 2025-06-24
-- 修改人: [陶醉]
-- 功能说明: 通用附件表
-- 关联类型字段增加注释
ALTER TABLE `file`
MODIFY COLUMN `relevance_type` tinyint  NULL DEFAULT NULL COMMENT '关联类型（1商品2问答3消息4店铺,5内容6注册7平台消息8评价）' AFTER `relevance_id`;
-- =======================================================================================
-- 执行日期: 2025-06-26
-- 修改人: [谭飞]
-- 功能说明: 计划表审核人员信息
-- 新增审核人员id和姓名
ALTER TABLE `plan`
ADD COLUMN `auditor_id` varchar(64) DEFAULT NULL COMMENT '审核人员ID' ,
ADD COLUMN `auditor_name` varchar(64) DEFAULT NULL COMMENT '审核人员名称' ,
ADD COLUMN `tt_push_status` tinyint(1) DEFAULT 0 COMMENT 'TT推送状态(0未推送 1待办已推送 2已办已推送 -1推送失败)';;
-- =======================================================================================
-- 执行日期: 2025-07-01
-- 修改人: [万启乐]
-- 功能说明: 内容表
-- 新增是否重要is_important_notice
ALTER TABLE `content`
ADD COLUMN `is_important_notice` INT NULL DEFAULT NULL COMMENT '重要通知: 1是 2:否';
-- =======================================================================================
-- 执行日期: 2025-07-01
-- 修改人: [万启乐]
-- 功能说明: 公告操作日志
-- =======================================================================================
CREATE TABLE `content_log` (
	`id` VARCHAR(255) NOT NULL DEFAULT '0' COMMENT 'id' COLLATE 'utf8mb4_0900_ai_ci',
	`content_id` CHAR(36) NOT NULL COMMENT '内容id' COLLATE 'utf8mb4_0900_ai_ci',
	`type` INT NULL DEFAULT NULL COMMENT '操作类型',
	`gmt_create` DATETIME NULL DEFAULT NULL COMMENT '创建时间',
	`gmt_modified` DATETIME NULL DEFAULT NULL COMMENT '更新时间',
	`founder_id` CHAR(36) NULL DEFAULT NULL COMMENT '创建人Id' COLLATE 'utf8mb4_0900_ai_ci',
	`founder_name` VARCHAR(50) NULL DEFAULT NULL COMMENT '创建人名称' COLLATE 'utf8mb4_0900_ai_ci',
	`modify_name` VARCHAR(50) NULL DEFAULT NULL COMMENT '修改人名称' COLLATE 'utf8mb4_0900_ai_ci',
	`modify_id` VARCHAR(50) NULL DEFAULT NULL COMMENT '修改人id' COLLATE 'utf8mb4_0900_ai_ci',
	PRIMARY KEY (`id`) USING BTREE
)COMMENT='公告操作日志' COLLATE='utf8mb4_0900_ai_ci' ENGINE=InnoDB;
-- =======================================================================================
-- 执行日期: 2025-07-01
-- 修改人: [喻清太]
-- 功能说明: 新建流程配置表
-- =======================================================================================
CREATE TABLE `process_config` (
  `process_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '流程ID',
  `process_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '流程名称',
  `system_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '系统名称',
  `system_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '系统编号',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `founder_id` varchar(64) NOT NULL COMMENT '创建人ID',
  `founder_name` varchar(50) NOT NULL COMMENT '创建人名称',
  `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人ID',
  `modify_name` varchar(50) DEFAULT NULL COMMENT '修改人名称',
  `gmt_create` datetime NOT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `is_delete` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除： -1: 删除 0:未删除',
  `sort` int DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`process_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='流程配置'
-- =======================================================================================
-- 执行日期: 2025-07-01
-- 修改人: [喻清太]
-- 功能说明: 新建流程实例表
-- =======================================================================================
CREATE TABLE `process_instance` (
  `process_instance_id` varchar(64) NOT NULL COMMENT '流程实例ID',
  `process_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '流程ID',
  `current_node_id` varchar(255) DEFAULT NULL COMMENT '当前节点ID',
  `business_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '业务表主键 ID',
  `status` tinyint NOT NULL COMMENT '实例状态(0运行中 1已完成 2已中止)',
  `operater_id` varchar(64) DEFAULT NULL COMMENT '操作人ID',
  `operater_name` varchar(50) DEFAULT NULL COMMENT '操作人名称',
  `founder_id` varchar(64) NOT NULL COMMENT '创建人ID',
  `founder_name` varchar(50) NOT NULL COMMENT '创建人名称',
  `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人ID',
  `modify_name` varchar(50) DEFAULT NULL COMMENT '修改人名称',
  `gmt_create` datetime NOT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `is_delete` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除： -1: 删除 0:未删除',
  `sort` int DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`process_instance_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='流程实例'
-- =======================================================================================
-- 执行日期: 2025-07-01
-- 修改人: [喻清太]
-- 功能说明: 新建流程节点表
-- =======================================================================================
CREATE TABLE `process_node` (
  `process_node_id` varchar(64) NOT NULL COMMENT '审批流程节点表ID',
  `process_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '流程ID',
  `node_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '节点编号',
  `node_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '节点名称0:提交 1:审核 2:审定',
  `founder_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人ID',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人ID',
  `modify_name` varchar(50) DEFAULT NULL COMMENT '修改人名称',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `is_delete` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除： -1: 删除 0:未删除',
  `sort` int DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`process_node_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='审批流程节点'
-- =======================================================================================
-- 执行日期: 2025-07-01
-- 修改人: [喻清太]
-- 功能说明: 新建流程节点操作记录表
-- =======================================================================================
CREATE TABLE `process_node_operation` (
  `process_node_operation_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '流程节点操作ID',
  `process_instance_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '流程实例ID',
  `process_node_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '审批流程节点表ID',
  `operation` tinyint NOT NULL COMMENT '操作类型(0提交 1通过 2不通过)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `operater_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '操作人ID',
  `operater_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '操作人名称',
  `founder_id` varchar(64) NOT NULL COMMENT '创建人ID',
  `founder_name` varchar(50) NOT NULL COMMENT '创建人名称',
  `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人ID',
  `modify_name` varchar(50) DEFAULT NULL COMMENT '修改人名称',
  `gmt_create` datetime NOT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `is_delete` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除： -1: 删除 0:未删除',
  `sort` int DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`process_node_operation_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='流程节点操作记录'
-- =======================================================================================
-- 执行日期: 2025-07-01
-- 修改人: [喻清太]
-- 功能说明: 新建审批流程角色表
-- =======================================================================================
CREATE TABLE `process_role` (
  `process_role_id` varchar(64) NOT NULL COMMENT '审批流程角色表ID',
  `process_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '流程ID',
  `process_node_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '审批流程节点表ID',
  `role_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '角色编号',
  `role_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色名称(0:供应商 1:机料内业 2:机料部门/科室 3:电商运营部内业人员 4:电商运营部负责人 5:财务部 6:分管领导)',
  `founder_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人ID',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人ID',
  `modify_name` varchar(50) DEFAULT NULL COMMENT '修改人名称',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `is_delete` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除： -1: 删除 0:未删除',
  `sort` int DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`process_role_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='审批流程角色'
-- =======================================================================================
-- 执行日期: 2025-07-01
-- 修改人: [喻清太]
-- 功能说明: 新建审批流程人员表
-- =======================================================================================
CREATE TABLE `process_user` (
  `process_user_id` varchar(64) NOT NULL COMMENT '审批流程人员表ID',
  `process_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '流程ID',
  `process_role_id` varchar(64) DEFAULT NULL COMMENT '审批流程角色表ID',
  `user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '人员ID',
  `user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '人员名称',
  `founder_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人ID',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `modify_id` varchar(64) DEFAULT NULL COMMENT '修改人ID',
  `modify_name` varchar(50) DEFAULT NULL COMMENT '修改人名称',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `is_delete` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除： -1: 删除 0:未删除',
  `sort` int DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`process_user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='审批流程人员'
-- =======================================================================================
-- 修改人: [曹然]
-- 功能说明: 新增
CREATE TABLE `plan_audit` (
    `id` VARCHAR(32) NOT NULL COMMENT '主键ID',
    `bill_id` VARCHAR(32) NOT NULL COMMENT '计划id',
    `reason` VARCHAR(255) COMMENT '原因',
    `gmt_create` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `founder_name` VARCHAR(64) COMMENT '创建人名称',
    `founder_id` VARCHAR(32) COMMENT '创建人Id',
    `modify_name` VARCHAR(64) COMMENT '修改人名称',
    `modify_id` VARCHAR(32) COMMENT '修改人Id',
    `is_delete` TINYINT(1) DEFAULT 0 COMMENT '逻辑删除 -1: 删除 0:未删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='计划审核表';
-- =======================================================================================
-- 执行日期: 2025-07-01
-- 修改人: [刘伟东]
-- 功能说明: 自营店出入库记录表
-- =======================================================================================
CREATE TABLE `self_operated_record`  (
  `record_id` varchar(64) NOT NULL COMMENT '记录id',
  `product_id` varchar(64)  NULL DEFAULT NULL COMMENT '商品id',
  `product_name` varchar(64)  NULL DEFAULT NULL COMMENT '商品名称',
  `class_id` varchar(64)  NULL DEFAULT NULL COMMENT '分类id',
  `class_path_name` varchar(64)  NULL DEFAULT NULL COMMENT '分类名称',
  `serial_num` varchar(64)  NULL DEFAULT NULL COMMENT '商品编码',
  `sell_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '销售价格',
  `original_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '原价',
  `tax_rate` decimal(18, 2) NULL DEFAULT NULL COMMENT '税率',
  `product_min_img` varchar(255)  NULL DEFAULT NULL COMMENT '商品小图',
  `cost_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '成本价',
  `profit_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '差价',
  `stock` decimal(18, 2) NULL DEFAULT NULL COMMENT '库存',
  `warehouse_id` varchar(64)  NULL DEFAULT NULL COMMENT '库房id',
  `operation_user` varchar(64)  NULL DEFAULT NULL COMMENT '操作人',
  `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`record_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '自营店出入库记录表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
-- =======================================================================================
-- 执行日期: 2025-07-01
-- 修改人: [刘伟东]
-- 功能说明: 二级供应商出入库记录表
-- =======================================================================================
CREATE TABLE `secondary_supplier_record`  (
  `record_id` varchar(64)  NOT NULL COMMENT '记录id',
  `order_sn` varchar(64)  NULL DEFAULT NULL COMMENT '订单号',
  `product_id` varchar(64)  NULL DEFAULT NULL COMMENT '商品id',
  `product_name` varchar(64) NULL DEFAULT NULL COMMENT '商品名称',
  `purchasing_agency_id` varchar(64)  NULL DEFAULT NULL COMMENT '采购机构id',
  `purchasing_agency_name` varchar(64)  NULL DEFAULT NULL COMMENT '采购机构',
  `record_type` tinyint NULL DEFAULT NULL COMMENT '记录类型 1-上架 2-下架',
  `product_type` tinyint NULL DEFAULT NULL COMMENT '商品类型：0 低值易耗品 1大宗临购 2、周转材料',
  `bid_rate_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '含税总金额',
  `num` decimal(18, 2) NULL DEFAULT NULL COMMENT '数量',
  `warehouse_id` varchar(64)  NULL DEFAULT NULL COMMENT '库房id',
  `operation_user_phone` varchar(64)  NULL DEFAULT NULL COMMENT '操作人电话',
  `operation_user` varchar(64)  NULL DEFAULT NULL COMMENT '操作人',
  `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `return_time` datetime NULL DEFAULT NULL COMMENT '申请退货时间',
  PRIMARY KEY (`record_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '二级供应商出入库记录表' ROW_FORMAT = DYNAMIC;

-- =======================================================================================
-- 执行日期: 2025-07-03
-- 修改人: [曹然]
-- 功能说明: 平台交易费明细表添加 税款相关字段
-- =======================================================================================
ALTER TABLE platform_deal_fee_dtl
ADD COLUMN no_tax_amount DECIMAL(18, 2) COMMENT '不含税金额',
ADD COLUMN tax_rate DECIMAL(5, 2) COMMENT '税率',
ADD COLUMN tax_amount DECIMAL(18, 2) COMMENT '税额';
-- =======================================================================================
-- 执行日期: 2025-07-03
-- 修改人: [曹然]
-- 功能说明: 平台交易费缴费记录 税款相关字段
-- =======================================================================================
ALTER TABLE platform_deal_fee_record
ADD COLUMN no_tax_amount DECIMAL(18, 2) COMMENT '不含税金额',
ADD COLUMN tax_rate DECIMAL(5, 2) COMMENT '税率',
ADD COLUMN tax_amount DECIMAL(18, 2) COMMENT '税额';
-- =======================================================================================
-- 执行日期: 2025-07-09
-- 修改人: [曹然]
-- 功能说明: 订单详情,计划详情添加账期
-- =======================================================================================
ALTER TABLE order_item ADD payment_period INT NULL COMMENT '账期 1-6月';
ALTER TABLE plan_detail ADD payment_period INT NULL COMMENT '账期 1-6月';



-- =======================================================================================
-- 执行日期: 2025-07-08
-- 修改人: [万启乐]
-- 功能说明: 文件关联表
-- =======================================================================================
CREATE TABLE `file_record_related` (
	`id` VARCHAR ( 64 ) NOT NULL COMMENT 'ID',
	`record_id` VARCHAR ( 64 ) NULL DEFAULT NULL COMMENT 'record_id',
	`related_id` VARCHAR ( 64 ) NULL DEFAULT NULL COMMENT '关联ID',
	`object_name` VARCHAR ( 255 ) CHARACTER
	SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '对象名称',
	`user_name` VARCHAR ( 255 ) CHARACTER
    	SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'user_name',
	`object_path` VARCHAR ( 255 ) CHARACTER
	SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '对象路径',
	`non_ip_object_path` VARCHAR ( 255 ) CHARACTER
	SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '不含IP/域名的对象路径',
	`bucket_name` VARCHAR ( 255 ) CHARACTER
	SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '桶名称',
	`object_size_kb` DECIMAL ( 18, 2 ) DEFAULT NULL COMMENT '对象大小kb',
	`object_size_mb` DECIMAL ( 18, 2 ) DEFAULT NULL COMMENT '对象大小mb',
	`gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
	PRIMARY KEY ( `id` ) USING BTREE
) ENGINE = INNODB CHARACTER
SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '文件关联表' ROW_FORMAT = DYNAMIC;
-- =======================================================================================
-- 执行日期: 2025-07-11
-- 修改人: [刘伟东]
-- 功能说明: 二级供应商出入库记录表
-- =======================================================================================
ALTER TABLE secondary_supplier_record
ADD COLUMN supplier_id VARCHAR ( 64 ) NULL DEFAULT NULL COMMENT '供应商id',
ADD COLUMN supplier_name VARCHAR ( 64 ) NULL DEFAULT NULL COMMENT '供应商名称',
ADD COLUMN settlement_info longtext NULL DEFAULT NULL  COMMENT '商品明细';
-- =======================================================================================
-- 执行日期: 2025-07-11
-- 修改人: [刘伟东]
-- 功能说明: 入库结算单
-- =======================================================================================
CREATE TABLE `inbound_settlement`  (
  `id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `reconciliation_id` varchar(64)  NULL DEFAULT NULL COMMENT '对账单id',
  `supplier_type` tinyint NULL DEFAULT NULL COMMENT '业务类型 1-零星采购 2-大宗商品 3-周转材料',
  `supplier_id` varchar(64)  NULL DEFAULT NULL COMMENT '供应商id',
  `supplier_name` varchar(64)  NULL DEFAULT NULL COMMENT '供方名称',
  `contract_no` varchar(64)  NULL DEFAULT NULL COMMENT '合同编号',
  `invoice_num` varchar(64)  NULL DEFAULT NULL COMMENT '发票字号',
  `inbound_type` tinyint NULL DEFAULT NULL COMMENT '入库方式  1-手动入库 2-自动入库',
  `audit_status` tinyint NULL DEFAULT NULL COMMENT '审核状态',
  `warehouse_id` varchar(64)  NULL DEFAULT NULL COMMENT '仓库id',
  `rate_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '含税总金额',
  `no_rate_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '不含税总金额',
  `num` decimal(18, 2) NULL DEFAULT NULL COMMENT '数量',
  `receive_name` varchar(64)  NULL DEFAULT NULL COMMENT '收件人',
  `receive_phone` varchar(64)  NULL DEFAULT NULL COMMENT '收件人电话',
  `remark` varchar(255)  NULL DEFAULT NULL COMMENT '备注',
  `account_period` varchar(64)  NULL DEFAULT NULL COMMENT '期数',
  `settlement_info` longtext  NULL COMMENT '结算单明细',
  `stored_warehouse_time` datetime NULL DEFAULT NULL COMMENT '入库时间',
  `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
-- =======================================================================================
-- 执行日期: 2025-07-11
-- 修改人: [刘伟东]
-- 功能说明: 出库结算单
-- ======================================================================================
CREATE TABLE `outbound_settlement`  (
  `id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `reconciliation_id` varchar(64)  NULL DEFAULT NULL COMMENT '对账单id',
  `supplier_type` tinyint NULL DEFAULT NULL COMMENT '业务类型 0-零星采购 1-大宗商品 2-周转材料',
  `supplier_id` varchar(64)  NULL DEFAULT NULL COMMENT '供应商id',
  `supplier_name` varchar(64)  NULL DEFAULT NULL COMMENT '供方名称',
  `purchasing_org_id` varchar(64)  NULL DEFAULT NULL COMMENT '收货方id',
  `purchasing_org_name` varchar(64)  NULL DEFAULT NULL COMMENT '收货单位',
  `contract_no` varchar(64)  NULL DEFAULT NULL COMMENT '合同编号',
  `invoice_num` varchar(64)  NULL DEFAULT NULL COMMENT '发票字号',
  `outbound_type` tinyint NULL DEFAULT NULL COMMENT '出库方式  1-手动出库 2-自动出库',
  `audit_status` tinyint NULL DEFAULT NULL COMMENT '审核状态',
  `warehouse_id` varchar(64)  NULL DEFAULT NULL COMMENT '仓库id',
  `xs_rate_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '含税总金额',
  `xs_no_rate_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '不含税总金额',
  `cg_rate_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '含税总金额',
  `cg_no_rate_amount` decimal(18, 2) NULL DEFAULT NULL COMMENT '不含税总金额',
  `num` decimal(18, 2) NULL DEFAULT NULL COMMENT '数量',
  `periods_num` varchar(64)  NULL DEFAULT NULL COMMENT '期数',
  `project_name` varchar(64)  NULL DEFAULT NULL COMMENT '项目名称',
  `project_address` varchar(64)  NULL DEFAULT NULL COMMENT '项目地址',
  `ticket_receiving_unit` varchar(64)  DEFAULT NULL COMMENT '受票单位',
  `ticket_receiving_unit_address` varchar(64)  NULL DEFAULT NULL COMMENT '受票单位地址',
  `ticket_receiving_unit_tax_no` varchar(64)  NULL DEFAULT NULL COMMENT '受票单位税号',
  `ticket_receiving_unit_phone` varchar(64)  NULL DEFAULT NULL COMMENT '受票单位电话',
  `ticket_receiving_unit_bank` varchar(64)  NULL DEFAULT NULL COMMENT '受票单位开户行',
  `ticket_receiving_unit_account` varchar(64)  NULL DEFAULT NULL COMMENT '受票单位账号',
  `receive_name` varchar(64)  NULL DEFAULT NULL COMMENT '发货人',
  `receive_phone` varchar(64)  NULL DEFAULT NULL COMMENT '发货人电话',
  `supplier_invoice_status` tinyint NULL DEFAULT NULL COMMENT '供应商发票是否开具 0-否 1-是',
  `remark` varchar(255)  NULL DEFAULT NULL COMMENT '备注',
  `account_period` varchar(64) NULL DEFAULT NULL COMMENT '期数',
  `settlement_info` longtext  NULL COMMENT '结算单明细',
  `outbound_time` datetime NULL DEFAULT NULL COMMENT '出库时间',
  `apply_invoice_time` datetime NULL DEFAULT NULL COMMENT '申请开票日期',
  `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;
SET FOREIGN_KEY_CHECKS = 1;

-- 执行日期: 2025-07-14
-- 修改人: [曹然]
-- 功能说明: 竞价采购表，添加延长截止时间原因字段
-- =======================================================================================
ALTER TABLE bidding_purchase ADD deadline_time_result INT NULL COMMENT '延长截止时间原因';

-- 执行日期: 2025-07-15
-- 修改人: [万启乐]
-- 功能说明: 广告表，添加空间位置字段
-- =======================================================================================
ALTER TABLE ad_picture ADD use_positioning INT NULL COMMENT '广告空间位置';

-- =======================================================================================
-- 执行日期: 2025-07-30
-- 修改人: [刘伟东]
-- 功能说明: 添加出入库结算的期数序列表
-- =======================================================================================
CREATE TABLE `stock_settlement_sequence`  (
  `serial_num` int NULL DEFAULT NULL COMMENT '序列号',
  `current_month` varchar(255)  NULL DEFAULT NULL COMMENT '当前月份',
  `type` int NULL DEFAULT NULL COMMENT '1-入库结算 2-出库结算',
  INDEX `month_idx`(`current_month`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

-- =======================================================================================
-- 执行日期: 2025-07-29
-- 修改人: [曹然]
-- 功能说明: 订单表，添加派单相关字段
-- =======================================================================================
ALTER TABLE orders
ADD COLUMN assign_state TINYINT(1) DEFAULT 0 COMMENT '派单状态(0未派单1已派单)',
ADD COLUMN assignee_id VARCHAR(64) DEFAULT NULL COMMENT '接单人id',
ADD COLUMN assignee_name VARCHAR(64) DEFAULT NULL COMMENT '接单人姓名',
ADD COLUMN assignee_time DATETIME DEFAULT NULL COMMENT '接单时间',
ADD COLUMN reject_reason VARCHAR(255) DEFAULT NULL COMMENT '退单原因';

-- =======================================================================================
-- 执行日期: 2025-07-31
-- 修改人: [刘伟东]
-- 功能说明: 入库结算单表，添加机构字段
-- =======================================================================================
ALTER TABLE inbound_settlement
ADD COLUMN receive_org_id VARCHAR(64) DEFAULT NULL COMMENT '采购单位Id',
ADD COLUMN receive_org_name VARCHAR(64) DEFAULT NULL COMMENT '采购单位名称';

-- =======================================================================================
-- 执行日期: 2025-07-31
-- 修改人: [吴国琼]
-- 功能说明: PCWP大经理部
-- =======================================================================================
CREATE TABLE `pcwp_conorg` (
  `recordid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '主键（记录ID）',
  `orgid` varchar(255) DEFAULT NULL COMMENT '机构ID',
  `orgname` varchar(255) DEFAULT NULL COMMENT '机构名称',
  `parentorgid` varchar(255) DEFAULT NULL COMMENT '上级机构ID(承建机构)',
  `parentorgname` varchar(255) DEFAULT NULL COMMENT '上级机构名称(承建机构)',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `mdmstate` varchar(255) DEFAULT NULL COMMENT 'MDM数据状态: -1: 删除数据, 0: 新增数据, 1: 修改数据',
  KEY `orgid` (`orgid`),
  KEY `recordid` (`recordid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='PCWP大经理部'

-- =======================================================================================
-- 执行日期: 2025-07-31
-- 修改人: [吴国琼]
-- 功能说明: 个人获奖
-- =======================================================================================
CREATE TABLE `pcwp_grhjinfos` (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '主键（唯一值）',
  `pname` varchar(255) DEFAULT NULL COMMENT '人名姓名',
  `pnumber` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '人员编码',
  `hjmc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '奖项名称',
  `hjsj` datetime DEFAULT NULL COMMENT '获奖时间',
  `bfdw` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '颁发单位',
  `lastupdatetime` datetime DEFAULT NULL COMMENT '最后更新时间',
  `mdmstate` int DEFAULT NULL COMMENT 'MDM数据状态: -1: 删除数据, 0: 新增数据, 1: 修改数据',
  KEY `pnumber` (`pnumber`),
  KEY `id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='个人获奖'

-- =======================================================================================
-- 执行日期: 2025-07-31
-- 修改人: [吴国琼]
-- 功能说明: PCWP_MQ数据推送历史记录表
-- =======================================================================================
CREATE TABLE `pcwp_mq_data` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `json_text` json DEFAULT NULL COMMENT '队列数据',
  `type` varchar(255) DEFAULT NULL COMMENT '队列routingkey',
  `delivery_tag` bigint DEFAULT NULL,
  `patch` int DEFAULT NULL COMMENT '数据片段序列号',
  `time` datetime DEFAULT NULL COMMENT '消息队列时间',
  `is_get` char(1) DEFAULT '0' COMMENT '是否消费（0未消费，1消费）',
  PRIMARY KEY (`id`),
  KEY `delivery_tag` (`delivery_tag`),
  KEY `patch` (`patch`)
) ENGINE=InnoDB AUTO_INCREMENT=110 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='PCWP_MQ数据推送历史记录表'

-- =======================================================================================
-- 执行日期: 2025-07-31
-- 修改人: [吴国琼]
-- 功能说明: PCWP机构表
-- =======================================================================================
CREATE TABLE `pcwp_org` (
  `orgid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '主键（PCWP机构ID）',
  `orgname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '机构名称',
  `parentorgid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'PCWP上级机构ID',
  `orgtype` int DEFAULT NULL COMMENT '机构类型: -1:未知\n1:集团\n2:分公司\n3:子公司\n4:经理部\n5:项目部\n6:股份\n7:事业部',
  `sort` int DEFAULT NULL COMMENT '排序',
  `status` int DEFAULT NULL COMMENT '状态: 0: 不可用, 1: 可用',
  `lastmodifytime` datetime DEFAULT NULL COMMENT '最后修改时间（最后更新时间）',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `hrorgid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'HR的机构ID（区分大小写）',
  `hrparentorgid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'HR上级机构ID（区分大小写）',
  `shortname` varchar(255) DEFAULT NULL COMMENT '简称',
  `shortcode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '简码',
  `orgproperty` int DEFAULT NULL COMMENT '机构属性: 0: 托管, 1: 直属',
  `oldorgid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '老系统机构ID',
  `oldorgname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '老系统机构名称',
  `mdmstate` int DEFAULT NULL COMMENT 'MDM数据状态: -1: 删除数据, 0: 新增数据, 1: 修改数据',
  KEY `orgid` (`orgid`),
  KEY `shortcode` (`shortcode`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='PCWP机构表'

-- =======================================================================================
-- 执行日期: 2025-07-31
-- 修改人: [吴国琼]
-- 功能说明: HR机构表
-- =======================================================================================
CREATE TABLE `pcwp_orginfos` (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '组织内码（主键，组织ID，区分大小写）',
  `name` varchar(255) DEFAULT NULL COMMENT '组织名称',
  `number` varchar(255) DEFAULT NULL COMMENT '组织编号',
  `parentid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '父级id（区分大小写）',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '类型（O：组织、D：部门）',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `flastupdatetime` datetime DEFAULT NULL COMMENT '上次更新时间（最后更新时间）',
  `orglayertypeid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '组织层级ID',
  `orglayertypenumber` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '组织层级编码（1:集团  \n3:公司  \n4:站/办事处/事业部\n7:项目部归集\n8:集团直管大经理部\n9:分子公司自管项目部\n11:部门）',
  `orglayertypename` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '组织层级名称',
  `djlbid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '项目部所属大经理部内码',
  `djlbnumber` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '项目部所属大经理部编码',
  `djlbname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '项目部所属大经理部名称',
  `glzzid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '上级法定组织',
  `glzznumber` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '上级法定组织编码',
  `glzzname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '上级法定组织名称',
  `sortcode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '排序码',
  `issealup` int DEFAULT NULL COMMENT '是否启用（0是启用，1 是停用）',
  `mdmstate` int DEFAULT NULL COMMENT 'MDM数据状态: -1: 删除数据, 0: 新增数据, 1: 修改数据',
  KEY `number` (`number`),
  KEY `id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='HR机构表'

-- =======================================================================================
-- 执行日期: 2025-07-31
-- 修改人: [吴国琼]
-- 功能说明: PCWP云中心用户权限
-- =======================================================================================
CREATE TABLE `pcwp_person_permissions` (
  `user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户id',
  `user_name` varchar(255) DEFAULT NULL COMMENT '用户姓名',
  `org_id` varchar(255) DEFAULT NULL COMMENT '组织机构id',
  `org_name` varchar(255) DEFAULT NULL COMMENT '组织机构名称',
  `short_code` varchar(255) DEFAULT NULL COMMENT '机构简码',
  `role_id` varchar(255) DEFAULT NULL COMMENT '角色id',
  `role_name` varchar(255) DEFAULT NULL COMMENT '角色名称'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='PCWP云中心用户权限'

-- =======================================================================================
-- 执行日期: 2025-07-31
-- 修改人: [吴国琼]
-- 功能说明: 人员基本信息
-- =======================================================================================
CREATE TABLE `pcwp_personinfos` (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '唯一值',
  `pname` varchar(255) DEFAULT NULL COMMENT '人名姓名',
  `pnumber` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '人员编码',
  `orgnumber` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '所属机构（主要任职机构）',
  `gender` varchar(255) DEFAULT NULL COMMENT '性别',
  `deptnumber` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '部门（主要任职机构下的部门）',
  `age` int DEFAULT NULL COMMENT '年龄',
  `idcard` varchar(255) DEFAULT NULL COMMENT '身份证号',
  `gw` varchar(255) DEFAULT NULL COMMENT '目前岗位',
  `xl` varchar(255) DEFAULT NULL COMMENT '学历',
  `gznx` float DEFAULT NULL COMMENT '工作年限',
  `xz` varchar(255) DEFAULT NULL COMMENT '学制',
  `bysj` datetime DEFAULT NULL COMMENT '毕业时间',
  `byyx` varchar(255) DEFAULT NULL COMMENT '毕业院校',
  `byzy` varchar(255) DEFAULT NULL COMMENT '毕业专业',
  `mobile` varchar(255) DEFAULT NULL COMMENT '联系电话',
  `sbbm` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '社保编码',
  `sbjndw` varchar(255) DEFAULT NULL COMMENT '社保缴纳单位',
  `yx` varchar(255) DEFAULT NULL COMMENT '邮箱',
  `tc` varchar(255) DEFAULT NULL COMMENT '个人特长描述',
  `lastupdatetime` datetime DEFAULT NULL COMMENT '最后更新时间',
  `mdmstate` int DEFAULT NULL COMMENT 'MDM数据状态: -1: 删除数据, 0: 新增数据,1：修改数据',
  KEY `pnumber` (`pnumber`) USING BTREE,
  KEY `id` (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='人员基本信息'

-- =======================================================================================
-- 执行日期: 2025-07-31
-- 修改人: [吴国琼]
-- 功能说明: PCWP项目表
-- =======================================================================================
CREATE TABLE `pcwp_project` (
  `projectid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '主键（工程概况id）',
  `projectno` varchar(255) DEFAULT NULL COMMENT '项目编号',
  `abbreviationname` varchar(255) DEFAULT NULL COMMENT '项目简称',
  `projectname` varchar(255) DEFAULT NULL COMMENT '项目名称',
  `startdate` datetime DEFAULT NULL COMMENT '计划开工日期',
  `enddate` datetime DEFAULT NULL COMMENT '计划竣工日期',
  `totalmonth` float DEFAULT NULL COMMENT '项目工期',
  `proptypeid1` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '项目属性1',
  `proptypename1` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '项目属性名称1',
  `proptypeid2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '项目属性2',
  `proptypename2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '项目属性名称2',
  `classid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '工程类别Id',
  `class` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '工程类别',
  `gradeid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '工程等级Id',
  `gread` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '工程等级',
  `bownerid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '业主单位ID',
  `bownername` varchar(255) DEFAULT NULL COMMENT '业主单位名称',
  `constructiontypeid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '施工类别Id',
  `constructiontype` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '施工类别',
  `designerid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设计单位id',
  `designername` varchar(255) DEFAULT NULL COMMENT '设计单位名称',
  `supervisorid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '监理单位id',
  `supervisorname` varchar(255) DEFAULT NULL COMMENT '监理单位名称',
  `begincol` float DEFAULT NULL COMMENT '起讫桩号(开始)',
  `endcol` float DEFAULT NULL COMMENT '起讫桩号(结束)',
  `long` float DEFAULT NULL COMMENT '起讫总共距离',
  `width` float DEFAULT NULL COMMENT '宽度',
  `address` varchar(255) DEFAULT NULL COMMENT '项目详细地址',
  `createdate` datetime DEFAULT NULL COMMENT '创建日期',
  `orgid` varchar(255) DEFAULT NULL COMMENT '承建单位id',
  `orgname` varchar(255) DEFAULT NULL COMMENT '承建单位名称',
  `agentid` varchar(255) DEFAULT NULL COMMENT '经办人ID',
  `agentname` varchar(255) DEFAULT NULL COMMENT '经办人名称',
  `remark` varchar(255) DEFAULT NULL COMMENT '说明',
  `attachid` varchar(255) DEFAULT NULL COMMENT '附件ID',
  `state` int DEFAULT NULL COMMENT '状态（0在建\n1已交工\n2已竣工\n-1删除）',
  `recorderid` varchar(255) DEFAULT NULL COMMENT '录入人id',
  `recordername` varchar(255) DEFAULT NULL COMMENT '录入人',
  `recordertime` datetime DEFAULT NULL COMMENT '录入时间',
  `lastmodifierid` varchar(255) DEFAULT NULL COMMENT '最后修改人id',
  `lastmodifier` varchar(255) DEFAULT NULL COMMENT '最后修改人',
  `lastmodifytime` datetime DEFAULT NULL COMMENT '最后修改日期',
  `lastauditorid` varchar(255) DEFAULT NULL COMMENT '审核人id',
  `lastauditor` varchar(255) DEFAULT NULL COMMENT '审核人',
  `lastaudittime` datetime DEFAULT NULL COMMENT '审核时间',
  `workid` varchar(255) DEFAULT NULL COMMENT '流程WorkId',
  `oldworkid` varchar(255) DEFAULT NULL COMMENT '上一流程WorkId',
  `contractamount` float DEFAULT NULL COMMENT '合同金额',
  `contractid` varchar(255) DEFAULT NULL COMMENT '合同id',
  `contractorid` varchar(255) DEFAULT NULL COMMENT 'ContractorId',
  `contractor` varchar(255) DEFAULT NULL COMMENT 'Contractor',
  `oldkeyid` int DEFAULT NULL COMMENT 'OldKeyId',
  `budgetcost` float DEFAULT NULL COMMENT 'BudgetCost',
  `completedtime` datetime DEFAULT NULL COMMENT 'CompletedTime',
  `finishedtime` datetime DEFAULT NULL COMMENT 'FinishedTime',
  `Isfinishedevaluation` int DEFAULT NULL COMMENT '是否完成项目经理考评',
  `finishedevaluationtime` datetime DEFAULT NULL COMMENT '完成项目经理考评的时间',
  `mdmstate` int DEFAULT NULL COMMENT 'MDM数据状态（-1：删除数据\n0：新增数据\n1：修改数据）',
  KEY `projectid` (`projectid`),
  KEY `orgid` (`orgid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='PCWP项目表'

-- =======================================================================================
-- 执行日期: 2025-07-31
-- 修改人: [吴国琼]
-- 功能说明: 任职项目经历
-- =======================================================================================
CREATE TABLE `pcwp_rzjlinfos` (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '主键（唯一值）',
  `pname` varchar(255) DEFAULT NULL COMMENT '人名姓名',
  `pnumber` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '人员编码',
  `rzxs` varchar(255) DEFAULT NULL COMMENT '任职形式',
  `rzgw` varchar(255) DEFAULT NULL COMMENT '任职岗位',
  `bmbm` varchar(255) DEFAULT NULL COMMENT '部门编码',
  `bmmc` varchar(255) DEFAULT NULL COMMENT '部门名称',
  `xmmc` varchar(255) DEFAULT NULL COMMENT '项目名称',
  `xmbm` varchar(255) DEFAULT NULL COMMENT '项目编码',
  `startdate` datetime DEFAULT NULL COMMENT '生效时间',
  `enddate` datetime DEFAULT NULL COMMENT '失效时间',
  `lastupdatetime` datetime DEFAULT NULL COMMENT '最后更新时间',
  `mdmstate` int DEFAULT NULL COMMENT 'MDM数据状态: -1: 删除数据, 0: 新增数据, 1: 修改数据',
  KEY `pnumber` (`pnumber`),
  KEY `id` (`id`),
  KEY `bmbm` (`bmbm`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='任职项目经历'

-- =======================================================================================
-- 执行日期: 2025-07-31
-- 修改人: [吴国琼]
-- 功能说明: TT用户信息
-- =======================================================================================
CREATE TABLE `pcwp_tt_account` (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '主键（用户ID）',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户姓名',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户登录名',
  `employeenumber` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用户员工号',
  `created` datetime DEFAULT NULL COMMENT '创建时间',
  `mdmstate` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'MDM数据状态: -1: 删除数据, 0: 新增数据, 1: 修改数据',
  KEY `employeenumber` (`employeenumber`),
  KEY `id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='TT用户信息'

-- =======================================================================================
-- 执行日期: 2025-07-31
-- 修改人: [吴国琼]
-- 功能说明: 职称信息
-- =======================================================================================
CREATE TABLE `pcwp_zcinfos` (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '主键（唯一值）',
  `pname` varchar(255) DEFAULT NULL COMMENT '人名姓名',
  `pnumber` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '人号编码',
  `zclbmc` varchar(255) DEFAULT NULL COMMENT '职称类别名称',
  `zcdj` varchar(255) DEFAULT NULL COMMENT '职称等级',
  `zyfl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '专业分类',
  `zymc` varchar(255) DEFAULT NULL COMMENT '专业名称',
  `zsnumber` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '证书编号',
  `fzdate` datetime DEFAULT NULL COMMENT '发证日期',
  `lastupdatetime` datetime DEFAULT NULL COMMENT '最后更新时间',
  `mdmstate` int DEFAULT NULL COMMENT 'MDM数据状态: -1: 删除数据, 0: 新增数据, 1: 修改数据',
  KEY `pnumber` (`pnumber`) USING BTREE,
  KEY `id` (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='职称信息'

-- =======================================================================================
-- 执行日期: 2025-07-31
-- 修改人: [吴国琼]
-- 功能说明: 执业资格/上岗证
-- =======================================================================================
CREATE TABLE `pcwp_zyzginfos` (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '主键（唯一值）',
  `pname` varchar(255) DEFAULT NULL COMMENT '人名姓名',
  `pnumber` varchar(255) DEFAULT NULL COMMENT '人号码',
  `zyzgzsmc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '执业资格类别名称',
  `zydj` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '等级',
  `zsnumber` varchar(255) DEFAULT NULL COMMENT '证件号码',
  `fzdate` datetime DEFAULT NULL COMMENT '发证日期',
  `lastupdatetime` datetime DEFAULT NULL COMMENT '最后更新时间',
  `mdmstate` int DEFAULT NULL COMMENT 'MDM数据状态: -1: 删除数据, 0: 新增数据, 1: 修改数据',
  KEY `pnumber` (`pnumber`),
  KEY `id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='执业资格/上岗证'

-- =======================================================================================
-- 执行日期: 2025-08-05
-- 修改人: [万启乐]
-- 功能说明: 竞价商品表，添加机构字段
-- =======================================================================================
ALTER TABLE bidding_product
ADD COLUMN monthly_difference VARCHAR(64) DEFAULT NULL COMMENT '账期月单',
ADD COLUMN unit_price_including_tax VARCHAR(64) DEFAULT NULL COMMENT '含税拟销售单价',
ADD COLUMN tax_inclusive_amount VARCHAR(64) DEFAULT NULL COMMENT '含税拟销售金额';

-- =======================================================================================
-- 执行日期: 2025-08-05
-- 修改人: [万启乐]
-- 功能说明: 竞价记录表，添加字段
-- =======================================================================================
ALTER TABLE bidding_purchase
ADD COLUMN cancel_result_type int DEFAULT NULL COMMENT '作废原因：1：数据错误；2：不符合业务要求；3：不在此功能录入；4：其他',
ADD COLUMN cancel_remark VARCHAR(64) DEFAULT NULL COMMENT '作废说明',
ADD COLUMN bidding_order_company VARCHAR(64) DEFAULT NULL COMMENT '中标公司',
ADD COLUMN operator_id VARCHAR(64) DEFAULT NULL COMMENT '经办人ID',
ADD COLUMN operator VARCHAR(64) DEFAULT NULL COMMENT '经办人';

-- =======================================================================================
-- 执行日期: 2025-08-05
-- 修改人: [万启乐]
-- 功能说明: 竞价记录表，添加字段
-- =======================================================================================
ALTER TABLE bidding_bid_record
ADD COLUMN bid_state int DEFAULT NULL COMMENT '0:未中标；1：已中标';

-- =======================================================================================
-- 执行日期: 2025-08-12
-- 修改人: [刘伟东]
-- 功能说明: 流程操作日志表，添加字段
-- =======================================================================================
ALTER TABLE process_node_operation
ADD COLUMN business_key  VARCHAR(64) DEFAULT NULL COMMENT '业务id';

-- 执行日期: 2025-08-15
-- 修改人: [张凡军]
-- 功能说明: 物资验收表，添加索引
-- =======================================================================================
ALTER TABLE `material_reconciliation`
ADD INDEX `idx_mr_org_name`(`purchasing_org_name` ASC) USING BTREE,
ADD INDEX `idx_mr_is_delete`(`is_delete` ASC) USING BTREE;

-- 执行日期: 2025-08-15
-- 修改人: [张凡军]
-- 功能说明: 物资验收明细表，添加索引
-- =======================================================================================
ALTER TABLE `material_reconciliation_dtl`
ADD INDEX `idx_mrd_is_delete`(`is_delete` ASC) USING BTREE,
ADD INDEX `idx_mrd_reconciliation`(`reconciliation_id` ASC) USING BTREE,
ADD INDEX `idx_mrd_order`(`order_id` ASC) USING BTREE;

-- 执行日期: 2025-08-15
-- 修改人: [张凡军]
-- 功能说明: HR机构表，添加索引
-- =======================================================================================
ALTER TABLE `pcwp_orginfos`
ADD INDEX `name`(`name` ASC) USING BTREE,
ADD INDEX `sort_code`(`sortcode` ASC) USING BTREE,
ADD INDEX `mdm_state`(`mdmstate` ASC) USING BTREE,
ADD INDEX `is_sealup`(`issealup` ASC) USING BTREE,
ADD INDEX `type`(`type` ASC) USING BTREE,
ADD INDEX `org_layertype_number`(`orglayertypenumber` ASC) USING BTREE;


-- 执行日期: 2025-08-15
-- 修改人: [吴国琼]
-- 功能说明: 系统菜单新表
-- =======================================================================================
CREATE TABLE `sys_menu1` (
  `menu_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '菜单id',
  `code` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '菜单编号',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '菜单名称',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '1' COMMENT '菜单类型（1菜单2按钮3资源）',
  `auth_label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '权限标识（可选）',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '菜单图标',
  `path_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '路由地址',
  `class_code` tinyint DEFAULT NULL COMMENT '所属平台（关联表sys_syslist的id字段）',
  `parent_menu_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '父级菜单id（1级为null）',
  `level` tinyint DEFAULT '1' COMMENT '菜单层级（0子系统 1：一级，2：二级，3：二级）',
  `sort` int DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT '1' COMMENT '状态（是否显示（0否1是））',
  `remarks` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '描述',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `is_delete` tinyint DEFAULT '0' COMMENT '逻辑删除 -1: 删除 0:未删除',
  `mall_type` tinyint(1) DEFAULT '0' COMMENT '商城类型：0物资商场, 1设备商城 ',
  `is_open` tinyint DEFAULT '0' COMMENT '是否公开（0否1是）',
  `show_dev` tinyint(1) DEFAULT NULL COMMENT '是否只测试环境展示（0否1是）',
  `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人名称',
  `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人id',
  `perms` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '授权(多个用逗号分隔，如：user:list,user:create)',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜单表'


-- 执行日期: 2025-08-15
-- 修改人: [吴国琼]
-- 功能说明: 用户角色关联新表
-- =======================================================================================
CREATE TABLE `sys_user_role1` (
  `id` int NOT NULL,
  `user_id` varchar(255) DEFAULT NULL,
  `role_id` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统用户角色关联表'

-- 执行日期: 2025-08-15
-- 修改人: [吴国琼]
-- 功能说明: 系统子系统信息表
-- =======================================================================================
CREATE TABLE `sys_syslist` (
  `id` int NOT NULL COMMENT '主键',
  `name` varchar(255) DEFAULT NULL COMMENT '系统名称',
  `is_delete` char(1) DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统列表'

-- 执行日期: 2025-08-15
-- 修改人: [吴国琼]
-- 功能说明: 系统机构类型信息表
-- =======================================================================================
CREATE TABLE `sys_jg` (
  `id` varchar(255) NOT NULL,
  `type` varchar(10) DEFAULT NULL COMMENT '机构类型',
  `name` varchar(255) DEFAULT NULL COMMENT '机构名称',
  `sort` varchar(50) DEFAULT NULL COMMENT '排序',
  `is_delete` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '0' COMMENT '逻辑删除 -1: 删除 0:未删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统角色机构类型分类'

-- =======================================================================================
-- 执行日期: 2025-08-12
-- 修改人: [万启乐]
-- 功能说明: 竞价表，添加字段
-- =======================================================================================
ALTER TABLE bidding_product
ADD COLUMN bid_price  float DEFAULT NULL COMMENT '不含税到场单价',
ADD COLUMN tax_rate  float DEFAULT NULL COMMENT '税率',
ADD COLUMN bid_rate_price  float DEFAULT NULL COMMENT '含税到场单价',
ADD COLUMN bid_rate_amount  float DEFAULT NULL COMMENT '含税总金额',
ADD COLUMN bid_amount  float DEFAULT NULL COMMENT '不含税总金额';

-- =======================================================================================
-- 执行日期: 2025-08-15
-- 修改人: [孙功夺]
-- 功能说明: 竞价表，添加字段
-- =======================================================================================
ALTER TABLE bidding_product
ADD COLUMN new_price decimal(10,2) DEFAULT NULL COMMENT '修改后的竞价价格',
ADD COLUMN is_update_price int DEFAULT '0' COMMENT '价格是否修改 1已修改 0未修改 2修改审批完成',
ADD COLUMN price_time datetime DEFAULT NULL COMMENT '价格更新时间';

- 执行日期: 2025-08-15
-- 修改人: [吴国琼]
-- 功能说明: 系统角色新表
-- =======================================================================================
CREATE TABLE `sys_role1` (
  `role_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色id',
  `code` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '角色编号',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '角色名称（对应界面中的"角色名称"输入框）',
  `sort` int DEFAULT NULL COMMENT '排序',
  `state` tinyint DEFAULT '1' COMMENT '状态（0禁用 1启用）',
  `remarks` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '描述（对应界面底部备注）',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '更新时间',
  `founder_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人Id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人名称',
  `is_delete` tinyint DEFAULT '0' COMMENT '逻辑删除 -1:删除 0:未删除',
  `mall_type` tinyint DEFAULT '0' COMMENT '商城类型：0物资商场 1设备商城',
  `keyword` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '关键字（对应界面搜索框）',
  `org_search` tinyint DEFAULT NULL COMMENT '机构数据权限（1本机及子级 2只看本级）',
  `category_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '所属平台（1后台管理平台 2供应商平台 3履约平台，对应界面"机构类型"下拉框）',
  `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人名称',
  `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人id',
  `enterprise_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '企业id',
  `jgtype` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '机构类型（冗余字段）',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='角色表（对应界面中的角色管理功能）'

-- =======================================================================================
- 执行日期: 2025-08-18
-- 修改人: [刘伟东]
-- 功能说明: 系统用户新表
-- =======================================================================================
CREATE TABLE `neo_user`  (
  `user_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户id',
  `interior_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '内部id',
  `user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户名',
  `user_number` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '用户编号',
  `org_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '机构id',
  `org_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '机构名称',
  `account` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '账号',
  `password` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户密码',
  `user_mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机号码',
  `email` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `user_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像(图片地址)',
  `user_img_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '图片记录id',
  `real_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '真实姓名',
  `nick_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '昵称',
  `gender` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '性别 1:男 0: 女',
  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '省份',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '城市',
  `county` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '区县',
  `detail_address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '详细地址',
  `birthday` date NULL DEFAULT NULL COMMENT '生日',
  `device_state` tinyint NOT NULL DEFAULT 1 COMMENT '装备状态（0停用1启用）',
  `material_state` tinyint NOT NULL DEFAULT 1 COMMENT '物资状态（0停用1启用）',
  `state` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '用户状态 0：初始（默认） 1：启用  2:禁用 ',
  `is_admin` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '是否为管理员  1：是  0：不是',
  `gmt_create` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `gmt_modified` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `gmt_login` datetime NULL DEFAULT NULL COMMENT '上次登录时间',
  `founder_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人id',
  `founder_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人名称',
  `is_material` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否为物资商城注册   1：是   0：否',
  `is_device` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否为设备商城注册   1：是   0：否',
  `enterprise_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '企业附加信息id',
  `is_internal_user` tinyint NOT NULL DEFAULT 0 COMMENT '是否为内部用户   1：是   0：否（默认：0）',
  `platform_admin` tinyint NULL DEFAULT NULL COMMENT '是否为平台管理员  1：是  0：不是（默认：0）',
  `sort` int NULL DEFAULT NULL COMMENT '排序',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  `mall_type` tinyint NULL DEFAULT NULL COMMENT '商城类型：0物资商场, 1设备商城 ',
  `is_delete` tinyint NULL DEFAULT NULL COMMENT '逻辑删除 -1: 删除 0:未删除',
  `wx_open_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '微信的openId',
  `login_count` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '登陆次数',
  `first_login` int UNSIGNED NULL DEFAULT 1 COMMENT '是否首次登录 1 是 0 否',
  `locked_state` int NULL DEFAULT 0 COMMENT '锁定状态(0正常 1锁定)',
  `pwd_change_date` datetime NULL DEFAULT NULL COMMENT '上次密码修改时间',
  `password_err_times` int NULL DEFAULT 0 COMMENT '密码错误次数',
  `lock_cause` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '锁定原因',
  `last_failed_login_time` datetime NULL DEFAULT NULL COMMENT '上一次密码错误时间',
  `attr_one` tinyint NOT NULL DEFAULT 0 COMMENT '备用1（目前是重置密码）',
  `is_show_bid` int NOT NULL DEFAULT 1 COMMENT '是否提示电子招标服务',
  `modify_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人名称',
  `modify_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人id',
  `out_user_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'TT用户编号',
  PRIMARY KEY (`user_id`) USING BTREE,
  INDEX `interior_id`(`user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
-- =======================================================================================
- 执行日期: 2025-08-18
-- 修改人: [刘伟东]
-- 功能说明: 系统用户角色关联表
-- =======================================================================================
CREATE TABLE `sys_user_role`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `org_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `role_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统用户角色关联表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;