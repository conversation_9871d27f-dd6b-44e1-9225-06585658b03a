package scrbg.meplat.mall.pcwp.org;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import scrbg.meplat.mall.pcwp.FeignConfig;
import scrbg.meplat.mall.pcwp.PcwpClient;
import scrbg.meplat.mall.pcwp.PcwpRes;
import scrbg.meplat.mall.pcwp.org.model.Org;
/**
 * 组织机构
 * 
 */
@FeignClient(name = "pcwp-org-service", url = "${mall.prodPcwp2Url}", configuration=FeignConfig.class)
public interface PcwpOrgClient extends PcwpClient{

    /**
     * 获取用户所在机构
     * @param userId
     * @return
     */
    @GetMapping("/hr/org/getOrgByUserId")
    PcwpRes<List<Org>> getOrgByUserId(@RequestParam("userId") String userId);

    /**
     * 获取用户所在机构分配的角色
     * @param orgId
     * @param userId
     * @param org
     * @param sysCode
     * @param token
     * @return
     */
    @GetMapping("/hr/role/getUserHasRoles")
    PcwpRes<List<String>> getUserHasRoles(
            @RequestParam("orgId") String orgId,
            @RequestParam("userId") String userId,
            @RequestHeader("org") String org,
            @RequestHeader("sysCode") String sysCode,
            @RequestHeader("token") String token);
    /**
     * 通过机构Id查找机构
     * @param userId
     * @return
     */
    @GetMapping("/hr/org/getOrgById")
    PcwpRes<Org> getOrgById(
            @RequestParam("orgId") String orgId,
            @RequestHeader("org") String org,
            @RequestHeader("sysCode") String sysCode,
            @RequestHeader("token") String token);
}
