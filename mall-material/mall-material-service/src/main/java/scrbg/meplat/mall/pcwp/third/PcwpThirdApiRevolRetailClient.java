package scrbg.meplat.mall.pcwp.third;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import scrbg.meplat.mall.pcwp.FeignConfig;
import scrbg.meplat.mall.pcwp.KeyedPayload;
import scrbg.meplat.mall.pcwp.PcwpClient;
import scrbg.meplat.mall.pcwp.PcwpPageRes;
import scrbg.meplat.mall.pcwp.PcwpRes;
import scrbg.meplat.mall.pcwp.third.model.RevolPlan;
import scrbg.meplat.mall.pcwp.third.model.RevolPlanPageQueryCondition;
import scrbg.meplat.mall.pcwp.third.model.RevolPlanPageQueryResult;
import scrbg.meplat.mall.pcwp.third.model.StatePlan;
import scrbg.meplat.mall.pcwp.third.model.UpdatePlanDtl;
/**
 * 第三方接口服务
 * 周转材料相关
 * 还未对接，这里暂时模拟一下
 */
@FeignClient(name = "pcwp-thirdapi-revol-retail-service", url = "${mall.prodPcwp2Url02}", configuration=FeignConfig.class)
@Profile("!mock-pcwp")
public interface PcwpThirdApiRevolRetailClient extends PcwpClient{
    /**
     * 保存推送的周转材料计划(提供给物资采购平台)
     * @param revolRetailPlan
     * @return
     */
    @PostMapping("/thirdapi/turnover/saveTemporaryDemandPlan")
    PcwpRes<String> saveRevolRetailPlan(@RequestBody RevolPlan revolRetailPlan,
                                                @RequestHeader("token") String token, 
                                                @RequestHeader("syscode") String syscode); 
    
    /**
     * 第三方接口服务-根据周转材料计划id获取大周转材料计划(提供给物资采购平台)
     * TODO pcwp未提供此接口
     *
     * @param id
     * @param token
     * @param sysCode
     * @return
     */
    @GetMapping("/todo")
    PcwpRes<StatePlan> getRevolRetailPlanById(
            @RequestParam("id") String id,
            @RequestHeader("token") String token,
            @RequestHeader("sysCode") String sysCode
    );
    
    /**
     * 反写周转材料计划商城订单数量
     *
     * @param payload
     * @param token
     * @param sysCode
     * @return 
     */
    @PostMapping("thirdapi/turnover/updateShopDtl")
    PcwpRes<Void> updatePlanDtl(
            @RequestBody KeyedPayload<List<UpdatePlanDtl>> payload,
            @RequestHeader("token") String token,
            @RequestHeader("sysCode") String sysCode
    );
    
    /**
     * 分页查询周转材料计划
     *
     * @param filter
     * @param token
     * @param sysCode
     * @return
     */
    @PostMapping("todo")
    PcwpRes<PcwpPageRes<RevolPlanPageQueryResult>> queryPageRevolPlan(
            @RequestBody RevolPlanPageQueryCondition filter,
            @RequestHeader("token") String token,
            @RequestHeader("sysCode") String sysCode
    );

    /**
     * 周才数据回滚 (推送验收,推送计划,反写暂扣及暂扣作废)(物资采购平台)
     * @param keyId
     * @param token
     * @param sysCode
     * @return
     */
    @GetMapping("/thirdapi/turnover/dataRollback")
    PcwpRes<Void> rollbackRevolPlan(
            @RequestParam("keyId") String keyId,
            @RequestHeader("token") String token,
            @RequestHeader("sysCode") String sysCode
    );
}
