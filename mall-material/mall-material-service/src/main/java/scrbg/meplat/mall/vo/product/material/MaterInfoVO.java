package scrbg.meplat.mall.vo.product.material;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.File;
import scrbg.meplat.mall.entity.RegionPrice;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-11-29 12:55
 */
@Data
public class MaterInfoVO {

    @ApiModelProperty(value = "提交状态（0默认1待提交2待确认3已确认4已拒绝）供方使用，")
    private Integer supplierSubmitState;

    @ApiModelProperty(value = "商品id")
    private String productId;

    @ApiModelProperty(value = "商品编码")
    private String serialNum;

    @ApiModelProperty(value = "商品名称")

    private String productName;


    @ApiModelProperty(value = "店铺id")

    private String shopId;
    @ApiModelProperty(value = "店铺名称")

    private String shopName;

    @ApiModelProperty(value = "分类id")

    private String classId;

    @ApiModelProperty(value = "分类路径")

    private String classPathName;


    @ApiModelProperty(value = "商品描述")

    private String productDescribe;


    @ApiModelProperty(value = "商品类型：0物资 1设备 2周材（设备租赁） 3周材（设备） 4二手设备 5租赁设备")

    private Integer productType;


    @ApiModelProperty(value = "商品状态（0待上架 1已上架 2已下架）")

    private Integer state;


    @ApiModelProperty(value = "商品关键字（,分隔）")

    private String productKeyword;


    @ApiModelProperty(value = "商品运费类型（0商家包邮）")

    private Integer productTransportType;


    @ApiModelProperty(value = "商品的最低价")

    private BigDecimal productMinPrice;


    @ApiModelProperty(value = "商品库id")

    private String productInventoryId;


    @ApiModelProperty(value = "关联外部id")

    private String relevanceId;


    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;


    @ApiModelProperty(value = "品牌id")

    private String brandId;





    @ApiModelProperty(value = "上架时间")
    private Date putawayDate;


    @ApiModelProperty(value = "销量")

    private BigDecimal soldNum;


    @ApiModelProperty(value = "商品访问量")

    private Integer productVisitNum;


    @ApiModelProperty(value = "省")

    private String province;


    @ApiModelProperty(value = "市")

    private String city;


    @ApiModelProperty(value = "县、区")

    private String county;


    @ApiModelProperty(value = "详细地址")

    private String detailedAddress;


    @ApiModelProperty(value = "经度")

    private BigDecimal longitude;


    @ApiModelProperty(value = "纬度")

    private BigDecimal latitude;


    @ApiModelProperty(value = "店铺排序")

    private Integer shopSort;


    @ApiModelProperty(value = "综合排序")

    private Integer synthesisSort;


    @ApiModelProperty(value = "商品小图")

    private String productMinImg;


    @ApiModelProperty(value = "关联名称（用于关联外部的名称唯一不修改）")

    private String relevanceName;

    @ApiModelProperty(value = "关联编号")

    private String relevanceNo;

    @ApiModelProperty(value = "是否完成商品编辑（0否1是）")

    private Integer isCompletion;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "商城类型：0物资商场, 1设备商城 ")
    private Integer mallType;

    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "更新时间")
    private Date gmtModified;

    @ApiModelProperty(value = "创建人名称")
    private String founderName;

    @ApiModelProperty(value = "创建人Id")
    private String founderId;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "逻辑删除 -1: 删除 0:未删除")
    private Integer isDelete;
    /**
     * 新加
     */

    @ApiModelProperty(value = "分类路径")
    private List<String> classPath;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;


    /**
     * file
     */
    @ApiModelProperty(value = "商品主图")
    private List<File> adminFile;

    @ApiModelProperty(value = "商品小图")
    private List<File> minFile;

    @ApiModelProperty(value = "商品图片")
    private List<File> productFiles;

    /**
     * sku
     */

    @ApiModelProperty(value = "skuid")
    private String skuId;

    @ApiModelProperty(value = "sku名称")
    private String skuName;

    @ApiModelProperty(value = "成本价")
    private BigDecimal costPrice;

    @ApiModelProperty(value = "原价")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "销售价格")
    private BigDecimal sellPrice;

    @ApiModelProperty(value = "库存")
    private BigDecimal stock;

    @ApiModelProperty(value = "规格单位")
    private String unit;

    @ApiModelProperty(value = "结算价")
    private BigDecimal settlePrice;
    @ApiModelProperty(value = "商品材质")

    private String productTexture;
    @ApiModelProperty(value = "副计量单位")
    private String secondUnit;
    @ApiModelProperty(value = "临购副单位系数")

    private BigDecimal secondUnitNum;
    @ApiModelProperty(value = "区域类型（1全区域,2区域）")
    private Integer isZone;


    @ApiModelProperty(value = "商品所属店铺类型（1自营店铺2内部店铺3外部店铺）")

    private Integer shopType;

    @ApiModelProperty(value = "商品对应区域价格")
    private List<RegionPrice> regionPrice;


    @ApiModelProperty(value = "年化率")
    private BigDecimal annualizedRate;

    @ApiModelProperty(value = "自营店商品-账期")
    private String accountPeriod;

    @ApiModelProperty(value = "自营店商品-采购进价")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "加成率 1固定 2自定义")
    private Integer markUp;

    @ApiModelProperty(value = "加成率")
    private BigDecimal markUpNum;

    @ApiModelProperty(value = "自定义加成审批状态（0提交 1负责人不通过 2负责人通过 3领导不通过 4领导通过）")
    private Integer jcState;

    @ApiModelProperty(value = "加成提交人姓名")
    private String jcTjName;

    @ApiModelProperty(value = "加成提交人id")
    private String jcTjId;

    @ApiModelProperty(value = "加成提交时间")
    private Date jcTjTime;

    @ApiModelProperty(value = "加成提交意见")
    private String jcTjYj;

    @ApiModelProperty(value = "加成负责人姓名")
    private String jcFzrName;

    @ApiModelProperty(value = "加成负责人id")
    private String jcFzrId;

    @ApiModelProperty(value = "加成负责人时间")
    private Date jcFzrTime;

    @ApiModelProperty(value = "加成负责人意见")
    private String jcFzrYj;

    @ApiModelProperty(value = "加成领导姓名")
    private String jcLdName;

    @ApiModelProperty(value = "加成领导id")
    private String jcLdId;

    @ApiModelProperty(value = "加成领导时间")
    private Date jcLdTime;

    @ApiModelProperty(value = "加成领导意见")
    private String jcLdYj;

    @ApiModelProperty(value = "价格类型：0 一口价 1 参考价")
    private Integer priceType;

    @ApiModelProperty(value = "(f92)价格是否修改:1已修改  0未修改  季度开始定时修改为0")
    private Integer priceState;
}
