package scrbg.meplat.mall.pcwp.third;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import scrbg.meplat.mall.pcwp.FeignConfig;
import scrbg.meplat.mall.pcwp.KeyedPayload;
import scrbg.meplat.mall.pcwp.PcwpClient;
import scrbg.meplat.mall.pcwp.PcwpPageRes;
import scrbg.meplat.mall.pcwp.PcwpRes;
import scrbg.meplat.mall.pcwp.third.model.RetailPlan;
import scrbg.meplat.mall.pcwp.third.model.SporadicPurchasePlanEx;
import scrbg.meplat.mall.pcwp.third.model.SporadicPurchasePlanPageQueryCondition;
import scrbg.meplat.mall.pcwp.third.model.SporadicPurchasePlanQueryResult;
import scrbg.meplat.mall.pcwp.third.model.UpdatePlanDtl;
/**
 * 第三方接口服务
 * 零星采购相关
 * 还未对接，这里暂时模拟一下
 */
@FeignClient(name = "pcwp-thirdapi-retail-service", url = "${mall.prodPcwp2Url02}", configuration=FeignConfig.class)
@Profile("!mock-pcwp")
public interface PcwpThirdApiRetailClient extends PcwpClient{
    /**
     * 推送零星采购计划
     * @param plan
     * @param token
     * @param syscode
     * @return 计划id
     */
    @PostMapping("/thirdapi/sporadicPurchasePlan/savePlan")
    PcwpRes<String> savePlan(@RequestBody KeyedPayload<RetailPlan> plan,
                                                @RequestHeader("token") String token, 
                                                @RequestHeader("syscode") String syscode);
    /**
     * 回滚保存推送的计划
     * @param plan
     * @param token
     * @param syscode
     * @return 计划id
     */
    @GetMapping("/thirdapi/sporadicPurchasePlan/rollBackSavePlan")
    PcwpRes<Void> rollbackPlan(@RequestParam String keyId,
                                                @RequestHeader("token") String token, 
                                                @RequestHeader("syscode") String syscode);

    /**
     * 第三方接口服务-反写零星采购计划商城订单数量(提供给物资采购平台)
     *
     * @param payload
     * @param token
     * @param sysCode
     * @return 
     */
    @PostMapping("/thirdapi/sporadicPurchasePlan/updateShopDtl")
    PcwpRes<Void> updatePlanDtl(
            @RequestBody KeyedPayload<List<UpdatePlanDtl>> payload,
            @RequestHeader("token") String token,
            @RequestHeader("sysCode") String sysCode
    );

    /**
     * 第三方接口服务-根据零星采购计划id获取零星采购计划
     *
     * @param id
     * @param token
     * @param sysCode
     * @return
     */
    @GetMapping("/thirdapi/sporadicPurchasePlan/getSporadicPurchasePlanById")
    PcwpRes<SporadicPurchasePlanEx> getRetailPlanById(
            @RequestParam("id") String id,
            @RequestHeader("token") String token,
            @RequestHeader("sysCode") String sysCode
    );

    /**
     * 第三方接口服务-分页查询零星采购计划
     *
     * @param filter
     * @param token
     * @param sysCode
     * @return
     */
    @PostMapping("/thirdapi/sporadicPurchasePlan/querySporadicPurchasePlanExPage")
    PcwpRes<PcwpPageRes<SporadicPurchasePlanQueryResult>> queryRetailPlansPage(
            @RequestBody SporadicPurchasePlanPageQueryCondition filter,
            @RequestHeader("token") String token,
            @RequestHeader("sysCode") String sysCode
    );
}
