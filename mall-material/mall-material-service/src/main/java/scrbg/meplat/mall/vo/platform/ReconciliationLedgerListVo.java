package scrbg.meplat.mall.vo.platform;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ReconciliationLedgerListVo {

    @ApiModelProperty(value = "项目id")
    private String purchasingOrgId;

    @ApiModelProperty(value = "项目名称")
    private String purchasingOrgName;

    @ApiModelProperty(value = "供应商id")
    private String supplierId;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "物资类型")
    private String reconciliationProductType;

    @ApiModelProperty(value = "对账类型")
    private String ReconciliationType;

    @ApiModelProperty(value = "对账单ID")
    private String reconciliationId;

    @ApiModelProperty(value = "对账单编号")
    private String reconciliationNo;

    @ApiModelProperty(value = "物资id")
    private String materialId;

    @ApiModelProperty(value = "物资名称")
    private String materialName;

    @ApiModelProperty(value = "规格型号")
    private String spec;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "对账数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "对账单价（含税）")
    private BigDecimal price;

    @ApiModelProperty(value = "对账单价（不含税）")
    private BigDecimal noRatePrice;

    @ApiModelProperty(value = "对账总金额")
    private BigDecimal acceptanceAmount;

    @ApiModelProperty(value = "税额（含税）")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "对账总金额（不含税）")
    private BigDecimal acceptanceNoRateAmount;

    @ApiModelProperty(value = "对账开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @ApiModelProperty(value = "对账结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    @ApiModelProperty(value = "状态")
    private Integer state;

    @ApiModelProperty(value = "查询合计金额")
    private BigDecimal countAmount;

    @ApiModelProperty(value = "查询合计金额不含税")
    private BigDecimal countNoRateAmount;

    @ApiModelProperty(value = "机构code")
    private String sortCode;

    @ApiModelProperty(value = "上级机构")
    private String parentSortCode;

    @ApiModelProperty(value = "组织层级编码")
    private String orglayertypenumber;

    @ApiModelProperty(value = "对账开始时间str")
    private String startTimeStr;

    @ApiModelProperty(value = "对账结束时间str")
    private String endTimeStr;

    @ApiModelProperty(value = "对账类型Str")
    @TableField(exist = false)
    private String ReconciliationTypeStr;

    @ApiModelProperty(value = "对账状态Str")
    @TableField(exist = false)
    private String stateStr;



}
