package scrbg.meplat.mall.pcwp.third;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import scrbg.meplat.mall.dto.plan.BulkRetailPlanEX;
import scrbg.meplat.mall.entity.MaterialDemand;
import scrbg.meplat.mall.pcwp.FeignConfig;
import scrbg.meplat.mall.pcwp.KeyedPayload;
import scrbg.meplat.mall.pcwp.PcwpClient;
import scrbg.meplat.mall.pcwp.PcwpPageRes;
import scrbg.meplat.mall.pcwp.PcwpRes;
import scrbg.meplat.mall.pcwp.third.model.BulkRetailPlanPageQueryCondition;
import scrbg.meplat.mall.pcwp.third.model.BulkRetailPlanPageQueryResult;
import scrbg.meplat.mall.pcwp.third.model.StatePlan;
import scrbg.meplat.mall.pcwp.third.model.TemporaryDemandPlanDtlParams;
import scrbg.meplat.mall.pcwp.third.model.UpdatePlanDtl;
import scrbg.meplat.mall.pcwp.third.model.VerifyPlan;

/**
 * 第三方接口服务
 * 大宗临购相关
 * 还未对接，这里暂时模拟一下
 */
@FeignClient(name = "pcwp-thirdapi-bulk-retail-service", url = "${mall.prodPcwp2Url02}", configuration = FeignConfig.class)
@Profile("!mock-pcwp")
public interface PcwpThirdApiBulkRetailClient extends PcwpClient {
    /**
     * 验证PCWP总计划是否有同名称、同规格、且有剩余数量的物资
     *
     * @param BulkRetailPlanSave
     * @return
     */
    @PostMapping("/thirdapi/todo")
    PcwpRes<Boolean> verifyPlan(@RequestBody VerifyPlan verifyPlan,
                                @RequestHeader("token") String token,
                                @RequestHeader("syscode") String syscode);

    /**
     * 保存推送的大宗临购计划(提供给物资采购平台)
     *
     * @param BulkRetailPlanSave
     * @return
     */
    @PostMapping("/thirdapi/bulkRetailPlan/saveBulkRetailPlan")
    PcwpRes<String> saveBulkRetailPlan(@RequestBody KeyedPayload<BulkRetailPlanEX> BulkRetailPlan,
                                       @RequestHeader("token") String token,
                                       @RequestHeader("syscode") String syscode);

    /**
     * 回滚大宗零购计划(提供给物资贸易平台)
     * @param keyId
     * @param token
     * @param syscode
     * @return
     */
    @GetMapping("/thirdapi/bulkRetailPlan/rollBackBulkRetailPlan")
    PcwpRes<Void> rollbackBulkRetailPlan(@RequestParam String keyId,
                                       @RequestHeader("token") String token,
                                       @RequestHeader("syscode") String syscode);

    /**
     * 第三方接口服务-根据大宗零购计划id获取大宗零购计划(提供给物资采购平台)
     *
     * @param id
     * @param token
     * @param sysCode
     * @return
     */
    @GetMapping("/thirdapi/bulkRetailPlan/getBulkRetailPlanById")
    PcwpRes<StatePlan> getBulkRetailPlanById(
            @RequestParam("id") String id,
            @RequestHeader("token") String token,
            @RequestHeader("sysCode") String sysCode
    );

    /**
     * 第三方接口服务-分页查询大宗临购计划
     *
     * @param filter
     * @param token
     * @param sysCode
     * @return
     */
    @PostMapping("/thirdapi/bulkRetailPlan/queryPageBulkRetailPlan")
    PcwpRes<PcwpPageRes<BulkRetailPlanPageQueryResult>> queryPageBulkRetailPlan(
            @RequestBody BulkRetailPlanPageQueryCondition filter,
            @RequestHeader("token") String token,
            @RequestHeader("sysCode") String sysCode
    );

    /**
     * 第三方接口服务-反写大宗零购计划商城订单数量(提供给物资采购平台)
     *
     * @param payload
     * @param token
     * @param sysCode
     * @return
     */
    @PostMapping("thirdapi/bulkRetailPlan/updateBulkRetailPlanDtl")
    PcwpRes<Void> updateBulkRetailPlanDtl(
            @RequestBody KeyedPayload<List<UpdatePlanDtl>> payload,
            @RequestHeader("token") String token,
            @RequestHeader("sysCode") String sysCode
    );

    /**
     * PCWP 退货 返还计划数量
     *
     * @param filter
     * @param token
     * @param sysCode
     * @return
     */
    @PostMapping("thirdapi/turnover/updateTemporaryDemandPlanDtl")
    PcwpRes<String> updateTemporaryDemandPlanDtl(
            @RequestBody TemporaryDemandPlanDtlParams filter,
            @RequestHeader("token") String token,
            @RequestHeader("sysCode") String sysCode
    );

    @GetMapping("thirdapi/turnover/findDtlByBillId")
    PcwpRes<List<MaterialDemand>> findDtlByBillId(
            @RequestParam String billId,
            @RequestHeader("token") String token,
            @RequestHeader("sysCode") String sysCode
    );
}
