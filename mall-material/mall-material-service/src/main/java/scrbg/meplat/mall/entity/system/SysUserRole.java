package scrbg.meplat.mall.entity.system;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@TableName("system_user_post")
@AllArgsConstructor
@NoArgsConstructor
public class SysUserRole implements Serializable{

    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 用户 ID
     *
     */
    private String userId;
    /**
     * 角色 ID
     *
     */
    private String roleId;


    /**
     * 组织id
     */
    private String orgId;

    public static SysUserRole init(String userId,String orgId, String roleId){
        SysUserRole role = new SysUserRole();
        role.setUserId(userId);
        role.setUserId(orgId);
        role.setRoleId(roleId);
        return role;
    }
}
