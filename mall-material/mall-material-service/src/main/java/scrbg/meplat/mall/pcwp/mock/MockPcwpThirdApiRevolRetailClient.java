package scrbg.meplat.mall.pcwp.mock;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import scrbg.meplat.mall.entity.plan.Plan;
import scrbg.meplat.mall.pcwp.KeyedPayload;
import scrbg.meplat.mall.pcwp.PcwpPageRes;
import scrbg.meplat.mall.pcwp.PcwpRes;
import scrbg.meplat.mall.pcwp.third.PcwpThirdApiRevolRetailClient;
import scrbg.meplat.mall.pcwp.third.model.RevolPlan;
import scrbg.meplat.mall.pcwp.third.model.RevolPlanPageQueryCondition;
import scrbg.meplat.mall.pcwp.third.model.RevolPlanPageQueryResult;
import scrbg.meplat.mall.pcwp.third.model.StatePlan;
import scrbg.meplat.mall.pcwp.third.model.UpdatePlanDtl;
import scrbg.meplat.mall.service.plan.PlanService;
/**
 * mock客户端
 */
@Component
@Profile("mock-pcwp")
@Primary
public class MockPcwpThirdApiRevolRetailClient implements PcwpThirdApiRevolRetailClient{
    @Autowired
    private PlanService planService;

    @Override
    public PcwpRes<PcwpPageRes<RevolPlanPageQueryResult>> queryPageRevolPlan(RevolPlanPageQueryCondition filter,
            String token, String sysCode) {
         List<RevolPlanPageQueryResult> results;
        if (filter.getPage()==1) {
            List<Plan> plans = planService.lambdaQuery().isNotNull(Plan::getPBillId).eq(Plan::getType, 2).list();
            results = plans.stream().map(p->{
                                                return RevolPlanPageQueryResult.builder()
                                                .billId(p.getPBillId())
                                                .state(1+"")
                                                .build();
                                            }).collect(Collectors.toList());
        }else{
            results = Collections.emptyList();
        }
        PcwpPageRes<RevolPlanPageQueryResult> pageRes = PcwpPageRes.<RevolPlanPageQueryResult>builder()
                                                                .currPage(1)
                                                                .list(results)
                                                                .totalCount(results.size())
                                                                .build();
        return PcwpRes.<PcwpPageRes<RevolPlanPageQueryResult>>builder()
                .code(200)
                .data(pageRes)
                .build();
    }

    @Override
    public PcwpRes<Void> updatePlanDtl(KeyedPayload<List<UpdatePlanDtl>> payload, String token, String sysCode) {
        return PcwpRes.<Void>builder().code(200).build();
    }

    @Override
    public PcwpRes<StatePlan> getRevolRetailPlanById(String id, String token, String sysCode) {
        throw new UnsupportedOperationException("Unimplemented method 'getRevolRetailPlanById'");
    }

    @Override
    public PcwpRes<String> saveRevolRetailPlan(RevolPlan revolRetailPlan, String token, String syscode) {
        throw new UnsupportedOperationException("Unimplemented method 'saveRevolRetailPlan'");
    }

    @Override
    public PcwpRes<Void> rollbackRevolPlan(String keyId, String token, String sysCode) {
        throw new UnsupportedOperationException("Unimplemented method 'rollbackRevolPlan'");
    }

    
}
