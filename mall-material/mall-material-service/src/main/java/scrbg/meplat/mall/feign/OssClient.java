package scrbg.meplat.mall.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Profile;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
/**
 * 文件服务
 */
@FeignClient(name = "mall-oss")
@Profile("!mock-pcwp")
public interface OssClient{
    @GetMapping("/oss/downloader")
    ResponseEntity<byte[]> downloadFileBytes(@RequestParam("recordId") String recordId, @RequestHeader("token") String token);   
}
