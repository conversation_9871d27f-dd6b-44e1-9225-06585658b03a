package scrbg.meplat.mall.config.scheduled.plan;

import java.util.List;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import scrbg.meplat.mall.entity.plan.Plan;
import scrbg.meplat.mall.service.plan.PlanService;
/**
 * 本地的推送到pcwp的计划只有状态为 1待审核 的有可能后续状态会产生变化（需要从pcwp端同步过来），且随着项目的运行，1待审核 状态的计划占整体比例应该很小
 * 所以这里的同步思路就是获取本地所有的状态为 1待审核 的pcwp计划数据，调用查询计划详情接口，然后更新本地数据(使用redis消息队列实现)
 */
// @Component
// @EnableScheduling
@RequiredArgsConstructor
@Slf4j
public class PlanProducer {

    private final PlanService planService;
    private final StringRedisTemplate redisTemplate;

    private static final String PLAN_QUEUE_KEY = "plan:sync:queue";

    @Scheduled(cron = "0 * * * * ?") // 每分钟一次
    public void fetchPlansAndPushToQueue() {
        Long size = redisTemplate.opsForList().size(PLAN_QUEUE_KEY);
        if (size!=null && size > 1000) {
            log.warn(PLAN_QUEUE_KEY+"队列产生堆积，对计量:{},放弃本次任务", size);
            return;
        }
        // TODO 考虑加一个时间限制，比如最近三个月, 或者更新频率随时间延长降低
        List<Plan> plans = planService.list(
                new LambdaQueryWrapper<Plan>().eq(Plan::getState, "1").isNotNull(Plan::getPBillId)
        );
        if (plans.isEmpty()) {
            return;
        }
        plans.forEach(plan -> redisTemplate.opsForList().leftPush(PLAN_QUEUE_KEY, plan.getBillId()));
        log.debug("同步计划任务推送到队列: {}条", plans.size());
    }
}
