package scrbg.meplat.mall.config.scheduled.plan;

import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import scrbg.meplat.mall.entity.plan.Plan;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.pcwp.PcwpRes;
import scrbg.meplat.mall.pcwp.PcwpService;
import scrbg.meplat.mall.pcwp.third.model.SporadicPurchasePlanEx;
import scrbg.meplat.mall.pcwp.third.model.StatePlan;
import scrbg.meplat.mall.service.plan.PlanService;

@Component
@RequiredArgsConstructor
@Slf4j
public class PlanConsumer {

    private final PlanService planService;
    private final PcwpService pcwpService;
    private final StringRedisTemplate redisTemplate;
    private final PlanStateMapper stateMapper;
    private final Executor planExecutor = Executors.newFixedThreadPool(1);

    private static final String PLAN_QUEUE_KEY = "plan:sync:queue";

    private static final int MAX_RETRY = 1;

    @PostConstruct
    public void startConsumers() {
        for (int i = 0; i < 10; i++) {
            planExecutor.execute(this::consumeLoop);
        }
    }

    private void consumeLoop() {
        while (true) {
            String billId = redisTemplate.opsForList().rightPop(PLAN_QUEUE_KEY, 5, TimeUnit.SECONDS);
            if (billId == null) {
                continue; // 队列空，继续轮询
            }
            try {
                handlePlan(billId);
            } catch (Exception e) {
                log.error("处理计划失败, 计划id: " + billId, e);
            }
        }
    }

    private void handlePlan(String billId) {
        Plan plan = planService.getById(billId);
        if (plan == null || !"1".equals(plan.getState())) {
            return; // 状态不匹配，跳过
        }
        
        for (int attempt = 1; attempt <= MAX_RETRY; attempt++) {
            try {
                int type = plan.getType();
                String pcwpState;
                if (type ==0) {
                    pcwpState = fetchRetailPlanState(plan.getPBillId());
                }else if (type == 1) {
                    pcwpState = fetchBulkRetailPlanState(plan.getPBillId());
                }else if (type == 2) {
                    pcwpState = fetchRevolRetailPlanState(plan.getPBillId());
                }else {
                    return;
                }
                String newState = stateMapper.map(pcwpState);
                if (newState.equals(plan.getState())) {
                    return;
                }
                boolean flag = planService.lambdaUpdate()
                    .eq(Plan::getBillId, billId)
                    .eq(Plan::getState, plan.getState())
                    .set(Plan::getState, newState)
                    .update();
                if (!flag) {
                    return;
                }
                log.debug("计划 " + billId + " 状态更新为: " + newState);
                return;

            } catch (Exception e) {
                log.error("第 " + attempt + " 次异常: ", e);
                try {
                    TimeUnit.SECONDS.sleep(1);
                } catch (InterruptedException ignored) {}
            }
        }
        log.warn("计划 " + billId + " 重试失败，放弃");
    }

    /**
     * 获取零星采购计划状态
     * @param pbillId
     * @return
     */
    private String fetchRetailPlanState(String pbillId) {
        PcwpRes<SporadicPurchasePlanEx> res = pcwpService.getRetailPlanById(pbillId);
        if (res == null || res.getCode() != 200 || res.getData() == null) {
            throw new BusinessException(res.getMessage());
        }
        return res.getData().getState();
    }

    /**
     * 获取大宗临购计划状态
     * @param pbillId
     * @return
     */
    private String fetchBulkRetailPlanState(String pbillId) {
        PcwpRes<StatePlan> res = pcwpService.getBulkRetailPlanById(pbillId);
        if (res == null || res.getCode() != 200 || res.getData() == null) {
            throw new BusinessException(res.getMessage());
        }
        return res.getData().getState();
    }

    /**
     * 获取周转材料计划状态
     * @param pbillId
     * @return
     */
    private String fetchRevolRetailPlanState(String pbillId) {
        PcwpRes<StatePlan> res = pcwpService.getRevolRetailPlanById(pbillId);
        if (res == null || res.getCode() != 200 || res.getData() == null) {
            throw new BusinessException(res.getMessage());
        }
        return res.getData().getState();
    }
}

