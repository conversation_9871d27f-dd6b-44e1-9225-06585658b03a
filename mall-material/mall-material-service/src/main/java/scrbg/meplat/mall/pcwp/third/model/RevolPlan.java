package scrbg.meplat.mall.pcwp.third.model;

import java.math.BigDecimal;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 需求计划请求对象
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RevolPlan {

    private DemandPlan demandPlan;
    private String keyId;
    private String orgId;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DemandPlan {
        private BigDecimal amount;
        private BigDecimal amountExcludingTax;
        private String annualDemandPlanBillId;
        private String annualDemandPlanBillNo;
        private String billNo;
        private Integer businessType;
        private List<DemandPlanDtl> demandPlanDtls;
        private BigDecimal deposit;
        private Integer isDeposit;
        private Integer isSignContract;
        private String planDate;
        private String preparer;
        private String preparerId;
        private String remarks;
        private BigDecimal taxAmount;
        private BigDecimal taxRate;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DemandPlanDtl {
        private BigDecimal amount;
        private String annualDemandPlanDtlId;
        private String billId;
        private String dtlId;
        private String endTime;
        private BigDecimal hasSettleAmount;
        private BigDecimal holdsQuantity;
        private Integer leaseTime;
        private String materialClassId;
        private String materialClassName;
        private String materialId;
        private String materialName;
        private BigDecimal notReceivedQuantity;
        private BigDecimal price;
        private Integer purchaseType;
        private BigDecimal quantity;
        private BigDecimal selectedTenderQuantity;
        private String spec;
        private String startTime;
        private BigDecimal surplusQuantity;
        private String texture;
        private String timeUnit;
        private String unit;
    }
}
