package scrbg.meplat.mall.config.scheduled.plan;

import java.util.function.Function;

import org.springframework.stereotype.Component;

import scrbg.meplat.mall.exception.BusinessException;

/**
 * 本地计划状态 计划状态(-1已作废 0待提交（已撤回）1待审核2已审核3已完成4审核不通过)
 * pcwp端计划状态 -1 已删除，0 草稿（待提交/已撤回），1 审核中，2 已审核，5 已作废，9 共享审核中
 * 两边状态的对应关系
 * 本地                pcwp
 * -1已作废            5 已作废 -1 已删除
 * 0待提交（已撤回）
 * 1待审核             1 审核中 9 共享审核中 0 草稿（待提交/已撤回）
 * 2已审核             2 已审核
 * 3已完成             
 * 4审核不通过    
 * 
 * 本地计划没有删除状态， 使用作废代表pcwp端的删除
 * 本地数据如果是pcwp的数据是直接推送到pcwp的，没有等待提交的状态。本地的待提交状态用于非pcwp计划的场景
 * 提交到pcwp的数据在pcwp端初始是草稿状态（已在测试环境确认），所以把pcwp 1 审核中 9 共享审核中 0 草稿（待提交/已撤回）都视为本地的待审核状态
 * 已完成是为了标记订单完成状态，审核不通过是应用于非pcwp计划的场景，这两个与pcwp端计划状态没有对应关系
 */
@Component
public class PlanStateMapper {
    private final Function<String, String> pcwpStateToLocalStateMapper = state -> {
        switch (state) {
            case "5":
            case "-1":
                return "-1";
            case "1":
            case "9":
            case "0":
                return "1";
            case "2":
                return "2";
        }
        throw new BusinessException("未知的状态: " + state);
    };

    public String map(String pcwpState) {
        return pcwpStateToLocalStateMapper.apply(pcwpState);
    }
}