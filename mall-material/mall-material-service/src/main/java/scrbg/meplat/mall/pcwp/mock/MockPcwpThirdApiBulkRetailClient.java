package scrbg.meplat.mall.pcwp.mock;

import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import scrbg.meplat.mall.dto.plan.BulkRetailPlanEX;
import scrbg.meplat.mall.entity.MaterialDemand;
import scrbg.meplat.mall.entity.plan.Plan;
import scrbg.meplat.mall.pcwp.KeyedPayload;
import scrbg.meplat.mall.pcwp.PcwpPageRes;
import scrbg.meplat.mall.pcwp.PcwpRes;
import scrbg.meplat.mall.pcwp.third.PcwpThirdApiBulkRetailClient;
import scrbg.meplat.mall.pcwp.third.model.BulkRetailPlanPageQueryCondition;
import scrbg.meplat.mall.pcwp.third.model.BulkRetailPlanPageQueryResult;
import scrbg.meplat.mall.pcwp.third.model.StatePlan;
import scrbg.meplat.mall.pcwp.third.model.TemporaryDemandPlanDtlParams;
import scrbg.meplat.mall.pcwp.third.model.UpdatePlanDtl;
import scrbg.meplat.mall.pcwp.third.model.VerifyPlan;
import scrbg.meplat.mall.service.plan.PlanService;

/**
 * mock客户端
 */
@Component
@Profile("mock-pcwp")
@Primary
public class MockPcwpThirdApiBulkRetailClient implements PcwpThirdApiBulkRetailClient {
    @Autowired
    private PlanService planService;

    @Override
    public PcwpRes<Boolean> verifyPlan(VerifyPlan verifyPlan, String token, String syscode) {
        return PcwpRes.<Boolean>builder().data(Boolean.TRUE).code(200).build();
    }

    @Override
    public PcwpRes<String> saveBulkRetailPlan(KeyedPayload<BulkRetailPlanEX> BulkRetailPlan, String token, String syscode) {
        return PcwpRes.<String>builder().data(UUID.randomUUID().toString().replaceAll("-", "")).code(200).message("Mock success").build();
    }

    @Override
    public PcwpRes<PcwpPageRes<BulkRetailPlanPageQueryResult>> queryPageBulkRetailPlan(
            BulkRetailPlanPageQueryCondition filter, String token, String sysCode) {
        List<BulkRetailPlanPageQueryResult> results;
        if (filter.getPage() == 1) {
            List<Plan> plans = planService.lambdaQuery().isNotNull(Plan::getPBillId).eq(Plan::getType, 1).list();
            results = plans.stream().map(p -> {
                return BulkRetailPlanPageQueryResult.builder()
                        .billId(p.getPBillId())
                        .state(1 + "")
                        .build();
            }).collect(Collectors.toList());
        } else {
            results = Collections.emptyList();
        }
        PcwpPageRes<BulkRetailPlanPageQueryResult> pageRes = PcwpPageRes.<BulkRetailPlanPageQueryResult>builder()
                .currPage(1)
                .list(results)
                .totalCount(results.size())
                .build();
        return PcwpRes.<PcwpPageRes<BulkRetailPlanPageQueryResult>>builder()
                .code(200)
                .data(pageRes)
                .build();
    }

    @Override
    public PcwpRes<Void> updateBulkRetailPlanDtl(KeyedPayload<List<UpdatePlanDtl>> payload, String token,
                                                 String sysCode) {
        return PcwpRes.<Void>builder().code(200).build();
    }

    @Override
    public PcwpRes<String> updateTemporaryDemandPlanDtl(TemporaryDemandPlanDtlParams filter, String token, String sysCode) {
        return PcwpRes.<String>builder().code(200).build();
    }

    @Override
    public PcwpRes<List<MaterialDemand>> findDtlByBillId(String billId, String token, String sysCode) {
        return PcwpRes.<List<MaterialDemand>>builder().code(200).build();
    }

    @Override
    public PcwpRes<StatePlan> getBulkRetailPlanById(String id, String token, String sysCode) {
        throw new UnsupportedOperationException("Unimplemented method 'getBulkRetailPlanById'");
    }

    @Override
    public PcwpRes<Void> rollbackBulkRetailPlan(String keyId, String token, String syscode) {
        throw new UnsupportedOperationException("Unimplemented method 'rollbackBulkRetailPlan'");
    }
}
