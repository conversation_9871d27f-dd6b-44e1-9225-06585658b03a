package scrbg.meplat.mall.util.cart;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;
import scrbg.meplat.mall.entity.Product;
import scrbg.meplat.mall.entity.ProductSku;
import scrbg.meplat.mall.entity.ShoppingCart;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.enums.product.OrderEnum;
import scrbg.meplat.mall.enums.product.ProductEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.ProductService;
import scrbg.meplat.mall.service.ProductSkuService;
import scrbg.meplat.mall.service.ShopService;
import scrbg.meplat.mall.service.ShoppingCartService;
import scrbg.meplat.mall.vo.product.ShoppingCartShopIdVO;

/**
 * 购物车校验工具类
 * 提供购物车相关的通用校验方法
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@Slf4j
public class CartValidationUtils {

    @Autowired
    private ProductService productService;

    @Autowired
    private ProductSkuService productSkuService;

    @Autowired
    private ShopService shopService;
    @Autowired
    private ShoppingCartService shoppingCartService;

    /**
     * 验证购物车信息是否存在
     *
     * @param cartVOS 购物车信息列表
     * @throws BusinessException 购物车信息不存在时抛出异常
     */
    public void validateCartExists(List<ShoppingCartShopIdVO> cartVOS) {
        if (CollectionUtils.isEmpty(cartVOS)) {
            throw new BusinessException("商品不存在或购物车信息不存在！");
        }
    }

    /**
     * 验证商品存在且已上架
     *
     * @param productId 商品ID
     * @return 商品信息
     * @throws BusinessException 商品不存在或已下架时抛出异常
     */
    public Product validateProductExists(String productId) {
        Product product = productService.getProductById(productId, ProductEnum.STATE_PUTAWAY.getCode());
        if (product == null) {
            Product byId = productService.getProductExcludeRemarkById(productId);
            if (byId != null) {
                throw new BusinessException("商品名为：【" + byId.getProductName() + "】的商品已下架或已被删除！");
            } else {
                throw new BusinessException(OrderEnum.RESULT_CODE_500201.getCode(), OrderEnum.RESULT_CODE_500201.getRemark());
            }
        }
        return product;
    }

    /**
     * 验证SKU存在且已上架
     *
     * @param skuId SKU ID
     * @param product 商品信息（用于异常提示）
     * @return SKU信息
     * @throws BusinessException SKU不存在或已下架时抛出异常
     */
    public ProductSku validateProductSkuExists(String skuId, Product product) {
        ProductSku productSku = productSkuService.getProductSkuById(skuId, PublicEnum.STATE_OPEN.getCode());
        if (productSku == null) {
            ProductSku byId = productSkuService.getById(skuId);
            if (byId != null) {
                throw new BusinessException("商品名为：【" + product.getProductName() + "】商品的【" + byId.getSkuName() + "】规格已下架或已被删除！");
            } else {
                throw new BusinessException("订单保存失败！");
            }
        }
        return productSku;
    }

    /**
     * 验证库存是否充足
     *
     * @param cart 购物车项
     * @param productSku SKU信息
     * @param product 商品信息（用于异常提示）
     * @throws BusinessException 库存不足时抛出异常
     */
    public void validateStockSufficient(ShoppingCart cart, ProductSku productSku, Product product) {
        if (cart.getCartNum().compareTo(productSku.getStock()) > 0) {
            throw new BusinessException("【" + product.getProductName() + "】商品的【" + productSku.getSkuName() + "】库存不足!");
        }
    }

    /**
     * 验证价格是否一致
     *
     * @param payPrice 支付价格
     * @param actualAmount 实际金额
     * @throws BusinessException 价格不一致时抛出异常
     */
    public void validatePriceConsistency(BigDecimal payPrice, BigDecimal actualAmount) {
        if (Math.abs(payPrice.subtract(actualAmount).doubleValue()) >= 0.01) {
            throw new BusinessException("订单商品价格发生变化，请确认后再次提交！");
        }
    }

    /**
     * 验证单个购物车项的完整性
     * 包括商品、SKU、库存的验证
     *
     * @param cart 购物车项
     * @return Object数组 [product, productSku]
     */
    public Object[] validateCartItem(ShoppingCart cart) {
        // 验证商品存在且已上架
        Product product = validateProductExists(cart.getProductId());

        // 验证SKU存在且已上架
        ProductSku productSku = validateProductSkuExists(cart.getSkuId(), product);

        // 验证库存充足
        validateStockSufficient(cart, productSku, product);

        return new Object[]{product, productSku};
    }

    /**
     * 批量验证购物车项
     *
     * @param cartVOS 购物车信息列表
     * @return 验证结果Map，key为shopId，value为商品名称拼接字符串
     */
    public Map<String, String> validateCartItems(List<ShoppingCartShopIdVO> cartVOS) {
        Map<String, String> untitledMap = new HashMap<>();

        for (ShoppingCartShopIdVO cartVO : cartVOS) {
            StringBuilder untitled = new StringBuilder();
            List<ShoppingCart> shoppingCarts = cartVO.getShoppingCarts();

            for (ShoppingCart cart : shoppingCarts) {
                Object[] validationResult = validateCartItem(cart);
                Product product = (Product) validationResult[0];

                // 拼接商品名称
                untitled.append(product.getProductName()).append(",");
            }

            // 移除最后一个逗号
            String productNames = untitled.toString();
            if (productNames.length() > 0) {
                productNames = productNames.substring(0, productNames.length() - 1);
            }
            untitledMap.put(cartVO.getShopId(), productNames);
        }

        return untitledMap;
    }

    /**
     * 计算购物车总金额
     *
     * @param cartVOS 购物车信息列表
     * @return 总金额
     */
    public BigDecimal calculateCartTotalAmount(List<ShoppingCartShopIdVO> cartVOS) {
        BigDecimal totalAmount = BigDecimal.ZERO;

        for (ShoppingCartShopIdVO cartVO : cartVOS) {
            for (ShoppingCart cart : cartVO.getShoppingCarts()) {
//                ProductSku productSku = productSkuService.getById(cart.getSkuId());
                if (cart.getProductPrice() != null) {
                    BigDecimal itemAmount = cart.getProductPrice()
                            .multiply(cart.getCartNum())
                            .setScale(2, RoundingMode.HALF_UP);
                    totalAmount = totalAmount.add(itemAmount);
                }
            }
        }

        return totalAmount;
    }


    /**
     * 完整的购物车验证流程
     * 包括存在性、商品、SKU、库存、价格的所有验证
     * @param cartVOS 购物车信息列表
     */
    public void validateCompleteCart(List<ShoppingCartShopIdVO> cartVOS) {

        // 1. 验证购物车存在
        validateCartExists(cartVOS);

        // 2. 验证购物车中的店铺是否被冻结
        for (ShoppingCartShopIdVO cartVO : cartVOS) {
            String shopId = cartVO.getShopId();
            if (!shopService.isCheckShopIdState(shopId)) {
                String shopName = shopService.getShopNameById(shopId);
                throw new BusinessException("店铺【" + shopName + "】已被冻结");
            }
        }

        BigDecimal actualAmount = BigDecimal.ZERO;
        String productType = "";

        // 3. 逐个验证购物车中的商品
        for (ShoppingCartShopIdVO cartVO : cartVOS) {
            StringBuilder untitled = new StringBuilder();
            List<ShoppingCart> shoppingCarts = cartVO.getShoppingCarts();

            for (ShoppingCart cart : shoppingCarts) {
                // 4. 验证商品存在且已上架
                Product product = validateProductExists(cart.getProductId());

                // 5. 验证SKU存在且已上架
                ProductSku productSku = validateProductSkuExists(cart.getSkuId(), product);

                // 6. 验证库存充足
                validateStockSufficient(cart, productSku, product);

                // 7. 拼接商品名称
                untitled.append(product.getProductName()).append(",");

                // TODO 现在金额应该从region_price中获取
                // 8. 计算金额
                BigDecimal itemAmount = shoppingCartService.getSellPrice(cart)
                        .multiply(cart.getCartNum())
                        .setScale(2, RoundingMode.HALF_UP);
                actualAmount = actualAmount.add(itemAmount);
            }
        }

        // 8. 验证价格一致性
        validatePriceConsistency(actualAmount, actualAmount);
    }
}
