package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import com.scrbg.common.utils.R;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.dto.order.OrderShipmentsQtyIsOkDTO;
import scrbg.meplat.mall.dto.order.UpdateCostPriceDTO;
import scrbg.meplat.mall.dto.order.UpdateOrderItemQtyDTO;
import scrbg.meplat.mall.dto.order.UpdateOrderPriceDTO;
import scrbg.meplat.mall.dto.plan.BulkRetailPlanDtlEX;
import scrbg.meplat.mall.dto.plan.BulkRetailPlanEX;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.MaterialMonthSupplyPlanDtlMapper;
import scrbg.meplat.mall.mapper.OrderItemMapper;
import scrbg.meplat.mall.mapper.OrdersMapper;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.util.*;
import scrbg.meplat.mall.util.countExcel.ExcelForWebUtil;
import scrbg.meplat.mall.vo.order.CetHistoryOrderItem;
import scrbg.meplat.mall.vo.platform.PlatformOrdersCountVO;
import scrbg.meplat.mall.vo.platform.TransactionProductVo;
import scrbg.meplat.mall.vo.product.StationMessageReceiveVO;
import scrbg.meplat.mall.vo.product.order.OrderItemVo;
import scrbg.meplat.mall.vo.shopManage.reportForms.PlatformProductFromVo;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @描述：订单项 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
@Service
public class OrderItemServiceImpl extends ServiceImpl<OrderItemMapper, OrderItem> implements OrderItemService {
    @Autowired
    MallConfig mallConfig;
    @Autowired
    SystemParamService systemParamService;
    @Autowired
    OrderShipService orderShipService;
    @Autowired
    StationMessageService stationMessageService;
    @Autowired
    private MaterialMonthSupplyPlanDtlMapper materialMonthSupplyPlanDtlMapper;
    @Autowired
    OrdersMapper ordersMapper;
    @Autowired
    OrderContactService orderContactService;
    @Autowired
    private RestTemplateUtils restTemplateUtils;

    @Autowired
    MaterialMonthSupplyPlanDtlService materialMonthSupplyPlanDtlService;


    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    ProductService productService;
    @Autowired
    OrderSelectPlanService orderSelectPlanService;
    @Autowired
    OrdersService ordersService;

    @Autowired
    OrderShipDtlService orderShipDtlService;

    @Autowired
    ShopService shopService;

    @Autowired
    EnterpriseInfoService enterpriseInfoService;
    @Autowired
    OrderItemMapper orderItemMapper;

    @Autowired
    ProductSkuService productSkuService;
    @Autowired
    OrderReturnItemService orderReturnItemService;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<OrderItem> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String orderId = (String) innerMap.get("orderId");
        String orderItemId = (String) innerMap.get("orderItemId");
        String keywords = (String) innerMap.get("keywords");
        Integer productType = (Integer) innerMap.get("productType");
        Integer mallType = mallConfig.mallType;
        q.orderByDesc(OrderItem::getGmtModified);
        q.orderByDesc(OrderItem::getOrderItemId);
        if (StringUtils.isNotEmpty(orderId)) {
            q.eq(OrderItem::getOrderId, orderId);
        }
        if (StringUtils.isNotEmpty(orderItemId)) {
            q.eq(OrderItem::getOrderItemId, orderItemId);
        }
        if (StringUtils.isNotBlank(keywords)) {
            q.like(OrderItem::getProductName, keywords);
        }
        q.eq(productType != null, OrderItem::getProductType, productType);
        q.eq(OrderItem::getMallType, mallType);
        q.eq(OrderItem::getIsDelete, PublicEnum.IS_DELETE_NO.getCode());
        IPage<OrderItem> page = this.page(
                new Query<OrderItem>().getPage(jsonObject),
                q
        );
        List<OrderItem> records = page.getRecords();
        for (OrderItem record : records) {
            if (record.getProductType() == 12) {
                BigDecimal newBuyCounts = record.getBuyCounts()
                        .add(record.getBuyCounts().multiply(BigDecimal.valueOf(0.1)));
                record.setAboveQty(newBuyCounts);
                record.setRelevanceName(record.getProductName());
            } else if (record.getProductType() == 13) {
                BigDecimal newBuyCounts = record.getBuyCounts()
                        .add(record.getBuyCounts().multiply(BigDecimal.valueOf(0.1)));
                record.setAboveQty(newBuyCounts);
            }
        }

        return new PageUtils(page);
    }

    @Override
    public List<OrderItem> getOrderItemByOrderIds(List<String> orderIds) {
        LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OrderItem::getOrderId, orderIds);
        List<OrderItem> list = list(wrapper);
        return list;
    }

    @Override
    public void create(OrderItem orderItem) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(orderItem);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(OrderItem orderItem) {
        super.updateById(orderItem);
    }

    @Override
    public void updateShipCounts(OrderItem orderItem) {
        String parentOrderItemId = orderItem.getParentOrderItemId();
        if (StringUtils.isNotBlank(parentOrderItemId)) {
            OrderItem parentOrderItem = getById(parentOrderItemId);
            if (parentOrderItem != null) {
                parentOrderItem.setShipCounts(orderItem.getShipCounts());
                parentOrderItem.setReturnCounts(orderItem.getReturnCounts());
                super.updateById(parentOrderItem);
            }
        } else {
            OrderItem son = getOrderItemByParentId(orderItem.getOrderItemId());
            if (son != null) {
                son.setShipCounts(orderItem.getShipCounts());
                son.setReturnCounts(orderItem.getReturnCounts());
                super.updateById(son);
            }
        }
        super.updateById(orderItem);
    }


    @Override
    public OrderItem getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    @Override
    public List<OrderItem> getOrderItemList(JSONObject jsonObject, LambdaQueryWrapper<OrderItem> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String orderId = (String) innerMap.get("orderId");
        String orderSn = (String) innerMap.get("orderSn");
        String orderItemId = (String) innerMap.get("orderItemId");
        if (StringUtils.isNotEmpty(orderId)) {
            q.eq(OrderItem::getOrderId, orderId);
        }
        if (StringUtils.isNotEmpty(orderSn)) {
            q.eq(OrderItem::getOrderSn, orderSn);
        }
        if (StringUtils.isNotEmpty(orderItemId)) {
            q.eq(OrderItem::getOrderItemId, orderItemId);
        }
        List<OrderItem> orderItemList = baseMapper.selectList(q);
        return orderItemList;
    }

    /**
     * 批量修改订单项
     *
     * @param orderItems
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateOrderItem(List<OrderItem> orderItems) {
        ArrayList<OrderItem> update = new ArrayList<>();
        for (OrderItem orderItem : orderItems) {
            OrderItem byId = getById(orderItem.getOrderItemId());
            if (byId != null) {
                BeanCopyIgnoreNullUtils.copyPropertiesIgnoreNull(orderItem, byId);
                update.add(byId);
            }
        }
        updateBatchById(update);

    }

    /**
     * 装备结算提交合同
     *
     * @param map
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deviceSubContract(Map map) {
        /**
         * planInfoList
         * billId "1632586253507497986"
         * billNo "SFGSTZGSLTJ6ZLJH20230306001"
         * count 6
         * dtlId "1632586521712267265"
         * equipmentName "挂篮"
         * orderItemId "1635162512419074049"
         *
         * orderAddPlan
         * billId "1632586253507497986"
         * dtlId "1632586521712267265"
         * orderItemId "1635162512419074049"
         */
        // 计划、数量列表
//        ArrayList<Map> planInfoList = (ArrayList<Map>) map.get("planInfoList");
//        ArrayList<Map> orderAddPlanMap = (ArrayList<Map>) map.get("orderAddPlan");
//        String orderSn = (String) map.get("orderSn");
//        if (StringUtils.isEmpty(orderSn)) {
//            throw new BusinessException(400, "未携带订单编号");
//        }
//        if (CollectionUtils.isEmpty(planInfoList)) {
//            throw new BusinessException(400, "未携带计划数据！");
//        }
//        if (CollectionUtils.isEmpty(orderAddPlanMap)) {
//            throw new BusinessException(400, "未携带计划数据！");
//        }
//        List<OrderItem> orderItems = lambdaQuery().eq(OrderItem::getOrderSn, orderSn).list();
//        Orders orders = ordersService.lambdaQuery().eq(Orders::getOrderSn, orderSn).one();
//        if (CollectionUtils.isEmpty(orderItems) || orders == null) {
//            throw new BusinessException(400, "订单不存在！");
//        }
//        ArrayList<ProductSku> productSkus = new ArrayList<>();
//        // 库存不存要下架的商品id
//        List<String> outProductId = new ArrayList<>();
//        // 验证库存
//        for (OrderItem orderItem : orderItems) {
//            String skuId = orderItem.getSkuId();
//            ProductSku productSku = productSkuService.lambdaQuery().eq(ProductSku::getSkuId, skuId)
//                    .select(ProductSku::getStock, ProductSku::getState).one();
//            if (productSku == null || productSku.getState() == 0) {
//                throw new BusinessException(400, "商品【" + orderItem.getProductName() + "】已下架或不存在！");
//            } else {
//                BigDecimal buyCounts = orderItem.getBuyCounts();
//                // 如果库存不足
//                int i = buyCounts.compareTo(productSku.getStock());
//                if (i == 1) {
//                    throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "【" + orderItem.getProductName() + "】商品库存不足!");
//                }
//                ProductSku productSku1 = new ProductSku();
//                BigDecimal newStock = productSku.getStock().subtract(buyCounts);
//
//                // 判断库存是否等于0如果等于0则下架商品
//                if (newStock.compareTo(BigDecimal.ZERO) == 0) {
//                    outProductId.add(productSku.getProductId());
//                }
//
//                productSku1.setSkuId(skuId);
//                productSku1.setStock(newStock);
//                productSkus.add(productSku1);
//            }
//        }
//        if (orders.getProductType() == 1) {
//            // 设备采购合同
//            submitContractDevice(orderItems, orders, planInfoList);
//            // 采购计划
//            ArrayList<Map> planMap = new ArrayList<>();
//            for (Map map1 : planInfoList) {
//                HashMap<Object, Object> map2 = new HashMap<>();
//                map2.put("billId", map1.get("billId"));
//                map2.put("dtlId", map1.get("dtlId"));
//                map2.put("planType", 1);
//                map2.put("quantity", map1.get("count"));
//                planMap.add(map2);
//            }
//            String url =  mallConfig.prodPcwp2Url02 + "/thirdapi/facility/contract/write/num";
//            HttpHeaders headers = new HttpHeaders();
//            String content = JSON.toJSONString(planMap);
//            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
//            headers.add("token", "UXp0UjJET2csMTY3MTA0MjIzMw.H83JNfMc8fze5OF-Vn4oSF4SqF4Wgm6PnZTK01adYvrQpLTZSN4ekA");
//            HttpEntity<String> request = new HttpEntity<>(content, headers);
//            R r = restTemplate.postForObject(url, request, R.class);
//            if (r.getCode() != 200) {
//                throw new BusinessException(r.getCode(), r.getMessage());
//            }
//        }
//        if (orders.getProductType() == 5) {
//            // 租赁合同
//            submitContractLeaseDevice(orderItems, orders, planInfoList);
//            // 租赁计划
//            ArrayList<Map> planMap = new ArrayList<>();
//            for (Map map1 : planInfoList) {
//                HashMap<Object, Object> map2 = new HashMap<>();
//                map2.put("billId", map1.get("billId"));
//                map2.put("dtlId", map1.get("dtlId"));
//                map2.put("planType", 0);
//                map2.put("quantity", map1.get("count"));
//                planMap.add(map2);
//            }
//            String url =mallConfig.prodPcwp2Url02 + "/thirdapi/facility/contract/write/num";
//            HttpHeaders headers = new HttpHeaders();
//            String content = JSON.toJSONString(planMap);
//            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
//            headers.add("token", "UXp0UjJET2csMTY3MTA0MjIzMw.H83JNfMc8fze5OF-Vn4oSF4SqF4Wgm6PnZTK01adYvrQpLTZSN4ekA");
//            HttpEntity<String> request = new HttpEntity<>(content, headers);
//            R r = restTemplate.postForObject(url, request, R.class);
//            if (r.getCode() != 200) {
//                throw new BusinessException(r.getCode(), r.getMessage());
//            }
//        }
//        if (orders.getProductType() == 2) {
//            // 周材租赁
//            submitContractWeekLeaseDevice(orderItems, orders, planInfoList);
//            // 周材租赁计划
//            ArrayList<Map> planMap = new ArrayList<>();
//            for (Map map1 : planInfoList) {
//                HashMap<Object, Object> map2 = new HashMap<>();
//                map2.put("billId", map1.get("billId"));
//                map2.put("dtlId", map1.get("dtlId"));
//                map2.put("type", 2);
//                map2.put("quantity", map1.get("count"));
//                planMap.add(map2);
//            }
//            String url =mallConfig.prodPcwp2Url02 + "/thirdapi/turnover/api/updatePlanSupplerQuantity";
//            HttpHeaders headers = new HttpHeaders();
//            String content = JSON.toJSONString(planMap);
//            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
//            headers.add("token", "UXp0UjJET2csMTY3MTA0MjIzMw.H83JNfMc8fze5OF-Vn4oSF4SqF4Wgm6PnZTK01adYvrQpLTZSN4ekA");
//            HttpEntity<String> request = new HttpEntity<>(content, headers);
//            R r = restTemplate.postForObject(url, request, R.class);
//            if (r.getCode() != 200) {
//                throw new BusinessException(r.getCode(), r.getMessage());
//            }
//        }
//        // 暂时废弃，没有这个类型
////        if (orders.getProductType() == 3) {
////            // 周材采购
////            // 周材采购计划
////            ArrayList<Map> planMap = new ArrayList<>();
////            for (Map map1 : planInfoList) {
////                HashMap<Object, Object> map2 = new HashMap<>();
////                map2.put("billId", map1.get("billId"));
////                map2.put("dtlId", map1.get("dtlId"));
////                map2.put("type", 1);
////                map2.put("quantity", map1.get("count"));
////                planMap.add(map2);
////            }
////            String url = "http://182.151.206.110:15103/thirdapi/turnover/api/updatePlanSupplerQuantity";
////            HttpHeaders headers = new HttpHeaders();
////            String content = JSON.toJSONString(planMap);
////            System.out.println("周材采购扣减JSON：" + content);
////            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
////            headers.add("token", "UXp0UjJET2csMTY3MTA0MjIzMw.H83JNfMc8fze5OF-Vn4oSF4SqF4Wgm6PnZTK01adYvrQpLTZSN4ekA");
////            HttpEntity<String> request = new HttpEntity<>(content, headers);
////            R r = restTemplate.postForObject(url, request, R.class);
////            System.out.println("周材采购计划扣减返回：" + r);
////            if (r.getCode() != 200) {
////                throw new BusinessException(r.getCode(), r.getMessage());
////            }
////        }
//
//        // 修改订单项，保存计划id
//        ArrayList<OrderItem> update = new ArrayList<>();
//        String orderId = null;
//        for (Map ordermAP : orderAddPlanMap) {
//            OrderItem orderItem = BeanUtils.mapToBean(ordermAP, OrderItem.class);
//            OrderItem byId = getById(orderItem.getOrderItemId());
//            if (byId != null) {
//                orderId = byId.getOrderId();
//                BeanCopyIgnoreNullUtils.copyPropertiesIgnoreNull(orderItem, byId);
//                update.add(byId);
//            }
//        }
//        updateBatchById(update);
//
//        // 保存扣减数量记录
//        for (Map map1 : planInfoList) {
//            OrderSelectPlan orderSelectPlan1 = BeanUtils.mapToBean(map1, OrderSelectPlan.class);
//            orderSelectPlan1.setOrderSn(orderSn);
//            orderSelectPlan1.setProductType(orders.getProductType());
//            boolean save = orderSelectPlanService.save(orderSelectPlan1);
//            if (!save) {
//                throw new BusinessException(400, "计划保存失败！");
//            }
//        }
//        // 订单状态
//        Orders orders2 = ordersService.lambdaQuery().eq(Orders::getOrderSn, orderSn).one();
//        orders2.setState(OrderEnum.STATE_FINISH.getCode());
//        orders2.setFlishTime(new Date());
//        ordersService.updateById(orders2);
//
//        // 扣减库存
//        if (!CollectionUtils.isEmpty(productSkus)) {
//            productSkuService.updateBatchById(productSkus);
//        }
//
//        // 库存为0下架商品
//        if (!CollectionUtils.isEmpty(outProductId)) {
//            if (!CollectionUtils.isEmpty(outProductId)) {
//                UpdateProductStateDTO updateProductStateDTO = new UpdateProductStateDTO();
//                updateProductStateDTO.setState(2);
//                updateProductStateDTO.setProductIds(outProductId);
//                productService.updateProductState(updateProductStateDTO);
//            }
//        }
    }

//    /**
//     * 采购合同
//     *
//     * @param orderItems
//     * @param orders
//     */
//    public void submitContractDevice(List<OrderItem> orderItems, Orders orders, ArrayList<Map> planInfoList) {
//        ArrayList<OrderContact> orderContacts = new ArrayList<>();
//        String url =mallConfig.prodPcwp2Url02 + "/thirdapi/purchase/contract/createEquipmentSales";
//        for (Map map : planInfoList) {
//            // 设备销售合同清单
//            // 新增设备销售合同
//            HashMap<String, Object> infoPVo = new HashMap<>();
//            infoPVo.put("contractAmount", orders.getActualAmount());
//            infoPVo.put("contractName", "装备采购合同");
//            infoPVo.put("orgId", ThreadLocalUtil.getCurrentUser().getOrgId());
//            infoPVo.put("orgName", ThreadLocalUtil.getCurrentUser().getEnterpriseName());
//            ArrayList<Map> equipmentSalesListVos = new ArrayList<>();
////                     * planInfoList
////                    * billId "1632586253507497986"
////                    * billNo "SFGSTZGSLTJ6ZLJH20230306001"
////                    * count 6
////                    * dtlId "1632586521712267265"
////                    * equipmentName "挂篮"
////                    * orderItemId "1635162512419074049"
//            HashMap<String, Object> equipmentSalesListVo = new HashMap<>();
//            String orderItemId = (String) map.get("orderItemId");
//            OrderItem orderItem = orderItems.stream().filter(m -> m.getOrderItemId().equals(orderItemId)).findAny().get();
//            Integer count = (Integer) map.get("count");
//            BigDecimal bigCount = new BigDecimal(count);
//            equipmentSalesListVo.put("amount", orderItem.getProductPrice().multiply(bigCount));
//            equipmentSalesListVo.put("name", map.get("equipmentName"));
//            equipmentSalesListVo.put("quantity", map.get("count"));
//            equipmentSalesListVo.put("specificationModel", orderItem.getSkuName());
////            ProductSku one = productSkuService.lambdaQuery().eq(ProductSku::getSkuId, orderItem.getSkuId()).select(ProductSku::getUnit).one();
////            equipmentSalesListVo.put("unit", one.getUnit());
//            equipmentSalesListVo.put("unitPrice", orderItem.getProductPrice());
//            // 计划
//            equipmentSalesListVo.put("planId", map.get("billId"));
//            equipmentSalesListVo.put("planNumber", map.get("billNo"));
//            equipmentSalesListVo.put("planName", orderItem.getRelevanceName());
//            equipmentSalesListVos.add(equipmentSalesListVo);
//            infoPVo.put("equipmentSalesListVos", equipmentSalesListVos);
//
//            // 设备销售合同主体信息(甲方)
//            HashMap<String, Object> firstParty = new HashMap<>();
////            EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getInteriorId, ThreadLocalUtil.getCurrentUser().getOrgId())
////                    .one();
//            firstParty.put("id", ThreadLocalUtil.getCurrentUser().getOrgId());
//            firstParty.put("name", ThreadLocalUtil.getCurrentUser().getEnterpriseName());
//            firstParty.put("type", 0);
//            infoPVo.put("firstParty", firstParty);
//
//            // 设备销售合同主体信息(乙方)
//            HashMap<String, Object> secondParty = new HashMap<>();
//            EnterpriseInfo enterpriseInfo1 = getEnter(orders.getShopId());
//            Shop shop = shopService.lambdaQuery().eq(Shop::getShopId, orders.getShopId())
//                    .select(Shop::getDetailedAddress, Shop::getContactNumber).one();
//            secondParty.put("address", shop.getDetailedAddress());
//            secondParty.put("orgId", enterpriseInfo1.getInteriorId());
//            secondParty.put("orgName", enterpriseInfo1.getEnterpriseName());
//            secondParty.put("phone", shop.getContactNumber());
//            secondParty.put("socialCode", enterpriseInfo1.getSocialCreditCode());
//            secondParty.put("type", 1); // 供应商
//            infoPVo.put("secondParty", secondParty);
//
//            HttpHeaders headers = new HttpHeaders();
//            String content = JSON.toJSONString(infoPVo);
//            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
//            headers.add("token", "UXp0UjJET2csMTY3MTA0MjIzMw.H83JNfMc8fze5OF-Vn4oSF4SqF4Wgm6PnZTK01adYvrQpLTZSN4ekA");
////        headers.add("org", "{\"orgId\":\"ad551eff9d03-8efe-2148-9ed8-64781e1e\",\"orgName\":\"四川路桥建设集团股份有限公司\",\"shortCode\":\"SRBC\",\"orgType\":1}");
//            HttpEntity<String> request = new HttpEntity<>(content, headers);
//            System.out.println("采购合同：" + content);
//            R r = restTemplate.postForObject(url, request, R.class);
//            OrderContact orderContact = new OrderContact();
//            orderContact.setOrderId(orders.getOrderId());
//            orderContact.setOrderSn(orders.getOrderSn());
//            orderContact.setOrderItemId((String) map.get("orderItemId"));
//            orderContact.setContactId((String) r.getData());
//            orderContacts.add(orderContact);
//        }
//        orderContactService.saveBatch(orderContacts);
//    }
//
//
//    /**
//     * 租赁合同
//     *
//     * @param orderItems
//     * @param orders
//     */
//    public void submitContractLeaseDevice(List<OrderItem> orderItems, Orders orders, ArrayList<Map> planInfoList) {
//        ArrayList<OrderContact> orderContacts = new ArrayList<>();
//        // 新增设备销售合同
//        String url =mallConfig.prodPcwp2Url02+ "/thirdapi/purchase/contract/createEquipmentLease";
//        for (Map map : planInfoList) {
//            HashMap<String, Object> infoPVo = new HashMap<>();
//            infoPVo.put("contractAmount", orders.getActualAmount());
//            infoPVo.put("contractName", "装备租赁合同");
//            infoPVo.put("orgId", ThreadLocalUtil.getCurrentUser().getOrgId());
//            infoPVo.put("orgName", ThreadLocalUtil.getCurrentUser().getEnterpriseName());
//            // 设备销售合同清单
//            ArrayList<Map> equipmentLeaseEqListVos = new ArrayList<>();
//            HashMap<String, Object> equipmentSalesListVo = new HashMap<>();
//            String orderItemId = (String) map.get("orderItemId");
//            OrderItem orderItem = orderItems.stream().filter(m -> m.getOrderItemId().equals(orderItemId)).findAny().get();
//            Integer count = (Integer) map.get("count");
//            BigDecimal bigCount = new BigDecimal(count);
//            equipmentSalesListVo.put("name", map.get("equipmentName"));
//            equipmentSalesListVo.put("amount", orderItem.getProductPrice().multiply(bigCount).multiply(orderItem.getLeaseNum()));
//            equipmentSalesListVo.put("quantity", map.get("count"));
//            equipmentSalesListVo.put("specificationModel", orderItem.getSkuName());
//            ProductSku one = productSkuService.lambdaQuery().eq(ProductSku::getSkuId, orderItem.getSkuId()).select(ProductSku::getUnit).one();
//            equipmentSalesListVo.put("unit", one.getUnit());
//            equipmentSalesListVo.put("unitPrice", orderItem.getProductPrice());
//            // 计划
//            equipmentSalesListVo.put("planId", map.get("billId"));
//            equipmentSalesListVo.put("planNumber", map.get("billNo"));
//            equipmentSalesListVo.put("planName", orderItem.getRelevanceName());
//            equipmentLeaseEqListVos.add(equipmentSalesListVo);
//
//            infoPVo.put("equipmentLeaseEqListVos", equipmentLeaseEqListVos);
//
//            // 设备销售合同主体信息(甲方)
//            HashMap<String, Object> firstParty = new HashMap<>();
////            EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getInteriorId, ThreadLocalUtil.getCurrentUser().getOrgId())
////                    .one();
//            firstParty.put("orgId", ThreadLocalUtil.getCurrentUser().getOrgId());
//            firstParty.put("orgName", ThreadLocalUtil.getCurrentUser().getEnterpriseName());
//            infoPVo.put("firstParty", firstParty);
//
//            // 设备销售合同主体信息(乙方)
//            HashMap<String, Object> secondParty = new HashMap<>();
//            EnterpriseInfo enterpriseInfo1 = getEnter(orders.getShopId());
//            Shop shop = shopService.lambdaQuery().eq(Shop::getShopId, orders.getShopId())
//                    .select(Shop::getDetailedAddress, Shop::getContactNumber).one();
//            secondParty.put("address", shop.getDetailedAddress());
//            secondParty.put("id", enterpriseInfo1.getInteriorId());
//            secondParty.put("name", enterpriseInfo1.getEnterpriseName());
//            secondParty.put("phone", shop.getContactNumber());
//            secondParty.put("socialCode", enterpriseInfo1.getSocialCreditCode());
//            secondParty.put("type", 1); // 供应商
//            infoPVo.put("secondParty", secondParty);
//
//            HttpHeaders headers = new HttpHeaders();
//            String content = JSON.toJSONString(infoPVo);
//            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
//            headers.add("token", "UXp0UjJET2csMTY3MTA0MjIzMw.H83JNfMc8fze5OF-Vn4oSF4SqF4Wgm6PnZTK01adYvrQpLTZSN4ekA");
//            HttpEntity<String> request = new HttpEntity<>(content, headers);
//            R r = restTemplate.postForObject(url, request, R.class);
//            OrderContact orderContact = new OrderContact();
//            orderContact.setOrderId(orders.getOrderId());
//            orderContact.setOrderSn(orders.getOrderSn());
//            orderContact.setOrderItemId((String) map.get("orderItemId"));
//            orderContact.setContactId((String) r.getData());
//            orderContacts.add(orderContact);
//        }
//        orderContactService.saveBatch(orderContacts);
//    }
//
//
//    /**
//     * 租赁周材合同
//     *
//     * @param orderItems
//     * @param orders
//     */
//    public void submitContractWeekLeaseDevice(List<OrderItem> orderItems, Orders orders, ArrayList<Map> planInfoList) {
//        ArrayList<OrderContact> orderContacts = new ArrayList<>();
//        // 新增设备销售合同
//        String url =mallConfig.prodPcwp2Url02 + "/thirdapi/purchase/contract/createMaterialRentalExpendInfo";
//        for (Map map : planInfoList) {
//            HashMap<String, Object> infoPVo = new HashMap<>();
//            infoPVo.put("contractAmount", orders.getActualAmount());
//            infoPVo.put("contractName", "周材租赁合同");
//            infoPVo.put("orgId", ThreadLocalUtil.getCurrentUser().getOrgId());
//            infoPVo.put("orgName", ThreadLocalUtil.getCurrentUser().getEnterpriseName());
//            // 设备销售合同清单
//            ArrayList<Map> equipmentLeaseEqListVos = new ArrayList<>();
//            HashMap<String, Object> equipmentSalesListVo = new HashMap<>();
//            String orderItemId = (String) map.get("orderItemId");
//            OrderItem orderItem = orderItems.stream().filter(m -> m.getOrderItemId().equals(orderItemId)).findAny().get();
//            Integer count = (Integer) map.get("count");
//            BigDecimal bigCount = new BigDecimal(count);
//            equipmentSalesListVo.put("name", map.get("equipmentName"));
//            equipmentSalesListVo.put("amount", orderItem.getProductPrice().multiply(bigCount).multiply(orderItem.getLeaseNum()));
//            equipmentSalesListVo.put("quantity", map.get("count"));
//            equipmentSalesListVo.put("specificationModel", orderItem.getSkuName());
//            ProductSku one = productSkuService.lambdaQuery().eq(ProductSku::getSkuId, orderItem.getSkuId()).select(ProductSku::getUnit).one();
//            equipmentSalesListVo.put("unit", one.getUnit());
//            equipmentSalesListVo.put("unitPrice", orderItem.getProductPrice());
//            // 计划
//            equipmentSalesListVo.put("planId", map.get("billId"));
//            equipmentSalesListVo.put("planNumber", map.get("billNo"));
//            equipmentSalesListVo.put("planName", orderItem.getRelevanceName());
//            equipmentLeaseEqListVos.add(equipmentSalesListVo);
//            infoPVo.put("equipmentLeaseEqListVos", equipmentLeaseEqListVos);
//
//            // 设备销售合同主体信息(甲方)
//            HashMap<String, Object> firstParty = new HashMap<>();
////            EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getInteriorId, ThreadLocalUtil.getCurrentUser().getOrgId())
////                    .one();
//            firstParty.put("orgId", ThreadLocalUtil.getCurrentUser().getOrgId());
//            firstParty.put("orgName",ThreadLocalUtil.getCurrentUser().getEnterpriseName());
//            infoPVo.put("firstParty", firstParty);
//
//            // 设备销售合同主体信息(乙方)
//            HashMap<String, Object> secondParty = new HashMap<>();
//            EnterpriseInfo enterpriseInfo1 = getEnter(orders.getShopId());
//            Shop shop = shopService.lambdaQuery().eq(Shop::getShopId, orders.getShopId())
//                    .select(Shop::getDetailedAddress, Shop::getContactNumber).one();
//            secondParty.put("address", shop.getDetailedAddress());
//            secondParty.put("id", enterpriseInfo1.getInteriorId());
//            secondParty.put("name", enterpriseInfo1.getEnterpriseName());
//            secondParty.put("phone", shop.getContactNumber());
//            secondParty.put("socialCode", enterpriseInfo1.getSocialCreditCode());
//            secondParty.put("type", 1); // 供应商
//            infoPVo.put("secondParty", secondParty);
//
//            HttpHeaders headers = new HttpHeaders();
//            String content = JSON.toJSONString(infoPVo);
//            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
//            headers.add("token", "UXp0UjJET2csMTY3MTA0MjIzMw.H83JNfMc8fze5OF-Vn4oSF4SqF4Wgm6PnZTK01adYvrQpLTZSN4ekA");
//            HttpEntity<String> request = new HttpEntity<>(content, headers);
//            R r = restTemplate.postForObject(url, request, R.class);
//            OrderContact orderContact = new OrderContact();
//            orderContact.setOrderId(orders.getOrderId());
//            orderContact.setOrderSn(orders.getOrderSn());
//            orderContact.setOrderItemId((String) map.get("orderItemId"));
//            orderContact.setContactId((String) r.getData());
//            orderContacts.add(orderContact);
//        }
//        orderContactService.saveBatch(orderContacts);
//    }

    /**
     * 根据店铺id查询企业
     *
     * @param shopId
     * @return
     */
    public EnterpriseInfo getEnter(String shopId) {
        Shop shop = shopService.lambdaQuery().eq(Shop::getShopId, shopId).select(Shop::getEnterpriseId).one();
        if (shop != null) {
            String enterpriseId = shop.getEnterpriseId();
            EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery()
                    .eq(EnterpriseInfo::getEnterpriseId, enterpriseId)
                    .one();
            return enterpriseInfo;
        } else {
            return new EnterpriseInfo();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderPrice(List<UpdateOrderPriceDTO> dtos) {
//        BigDecimal newPrice = new BigDecimal(0);
//        String orderItemId1 = dtos.get(0).getOrderItemId();
//        OrderItem orderItem = lambdaQuery().eq(OrderItem::getOrderItemId, orderItemId1)
//                .select(OrderItem::getOrderId).one();
//        String orderId = orderItem.getOrderId();
//        List<OrderItem> list = lambdaQuery().eq(OrderItem::getOrderId, orderId)
//                .select(OrderItem::getBuyCounts, OrderItem::getProductPrice, OrderItem::getOrderItemId,
//                        OrderItem::getVersion)
//                .list();
//        for (OrderItem item : list) {
//            String orderItemId = item.getOrderItemId();
//            for (UpdateOrderPriceDTO dto : dtos) {
//                if (orderItemId.equals(dto.getOrderItemId())) {
//                    item.setProductPrice(dto.getProductPrice());
//                }
//            }
//            BigDecimal multiply = item.getBuyCounts().multiply(item.getProductPrice());
//            item.setTotalAmount(multiply);
//            newPrice = newPrice.add(multiply);
//            updateById(item);
//        }
//        Orders orders = ordersService.getById(orderId);
//        orders.setActualAmount(newPrice);
//        ordersService.updateById(orders);
    }


    @Override
    public PageUtils orderItemListByOrderId(JSONObject jsonObject, QueryWrapper<OrderItemVo> orderItemVoQueryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String orderId = (String) innerMap.get("orderId");
        orderItemVoQueryWrapper.eq("oi.order_id", orderId);
        PageUtils<OrderItemVo> pageUtils = new PageUtils<>();
        IPage<OrderItemVo> pages = new Query<OrderItemVo>().getPage(jsonObject);
        List<OrderItemVo> list = orderItemMapper.orderItemListByOrderId(pageUtils, orderItemVoQueryWrapper);

        pages.setRecords(list);
        return new PageUtils(pages);
    }

    @Override
    public boolean save(OrderItem entity) {
        return super.save(entity);
    }

    @Override
    public void changReturnState(String orderItemId, BigDecimal count) {
        OrderItem orderItem = getById(orderItemId);
        OrderItem sonOrderId = getOrderItemByParentId(orderItemId);
        if (sonOrderId != null) {
            sonOrderId.setConfirmCounts(count);
            update(sonOrderId);
        }
        orderItem.setConfirmCounts(count);
        update(orderItem);
    }

    @Override
    public Orders isFilshAllOrderItem(String orderId) {
        Orders orders = ordersService.getById(orderId);
        LambdaQueryWrapper<OrderItem> q = new LambdaQueryWrapper<>();
        q.eq(OrderItem::getOrderId, orderId);
        List<OrderItem> list = list(q);
        int state = 10;
        for (OrderItem orderItem : list) {
            if (!orderItem.getBuyCounts().equals(orderItem.getShipCounts())) {
                state = 8;
            }
        }
        orders.setState(state);
        return orders;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderStateAndOrderItemByShip(OrderShip orderShip) {
        boolean flag = true;
        if (orderShip.getOtherOrderId() != null) {
            Orders sonOrders = ordersService.getById(orderShip.getOtherOrderId());
            sonOrders.setDeliveryTime(new Date());
            if (sonOrders.getState() != 9) {
                if (sonOrders != null) {
                    //获取所有子订单订单项
                    List<OrderItem> sonList = lambdaQuery().eq(OrderItem::getOrderSn, sonOrders.getOrderSn()).list();
                    if (sonList != null && sonList.size() > 0) {
                        for (OrderItem orderItem : sonList) {
                            //商城退货数量
                            BigDecimal returnCount = orderItem.getReturnCounts();
                            BigDecimal pcwpReturnCounts = orderItem.getPcwpReturn();
                            //购买数量
                            BigDecimal buyCounts = orderItem.getBuyCounts();
                            double key = systemParamService.listByCode("excessNum");
                            //大宗月供
                            if (orderItem.getProductType() == 12) {
                                buyCounts = orderItem.getBuyCounts().add(orderItem.getBuyCounts().multiply(BigDecimal.valueOf(100 / key)));
                                //大宗临购
                            } else if (orderItem.getProductType() == 13) {
                                buyCounts = orderItem.getBuyCounts().add(orderItem.getBuyCounts().multiply(BigDecimal.valueOf(100 / key)));
                            }
                            //购买数量
                            if (buyCounts.compareTo(orderItem.getShipCounts().add(returnCount).subtract(pcwpReturnCounts)) < 0) {
                                throw new BusinessException(500, "发货数量超出实际数量，请查看订单项为" + orderItem.getOrderItemId() + "的发货和退货数量");
                            } else if (orderItem.getBuyCounts().subtract(returnCount).compareTo(orderItem.getShipCounts().subtract(pcwpReturnCounts)) > 0) {
                                //购买数量-商城退货数量《=发货数量-pcwp退货数量 为待收货状态 ，其他为代发货状态
                                flag = false;
                                break;
                            }
                        }
                    }
                    if (flag) {
                        sonOrders.setState(9);
                    } else {
                        if (sonOrders.getState() == 6 || sonOrders.getState() == 8) {
                            sonOrders.setState(8);
                        }
                    }

                }

            }
            ordersService.update(sonOrders);
        } else {
            // 普通订单
            //获取所有订单项
            List<OrderItem> sonList = lambdaQuery().eq(OrderItem::getOrderId, orderShip.getOrderId()).list();
            if (sonList != null && sonList.size() > 0) {
                for (OrderItem orderItem : sonList) {
                    //商城退货数量
                    BigDecimal returnCount = orderItem.getReturnCounts();
                    // pcwp退货
                    BigDecimal pcwpReturnCounts = orderItem.getPcwpReturn();
                    BigDecimal shipCounts = orderItem.getShipCounts();
                    //购买数量
                    BigDecimal buyCounts = orderItem.getBuyCounts();
                    double key = systemParamService.listByCode("excessNum");
                    if (orderItem.getProductType() == 12) {//大宗月供
                        buyCounts = orderItem.getBuyCounts().add(orderItem.getBuyCounts().multiply(BigDecimal.valueOf(100 / key)));
                    } else if (orderItem.getProductType() == 13) {//大宗临购
                        buyCounts = orderItem.getBuyCounts().add(orderItem.getBuyCounts().multiply(BigDecimal.valueOf(100 / key)));
                    }
                    // 购买数量 < 发货数量 + 商城退货 - pcwp退货
                    if (buyCounts.compareTo(shipCounts.add(returnCount).subtract(pcwpReturnCounts)) < 0) {
                        throw new BusinessException(500, "发货数量超出实际数量，请查看订单项为" + orderItem.getOrderItemId() + "的发货和退货数量");
                    }
                    // 购买数量-商城退货数 > 发货数量-pcwp退货数量 ，未全部发货状态
                    if (buyCounts.subtract(returnCount).compareTo(shipCounts.subtract(pcwpReturnCounts)) > 0) {
                        flag = false;
                        break;
                    }
                }
            } else {
                throw new BusinessException(500, "未查询到订单项！");
            }
        }
        Orders orders = ordersService.getById(orderShip.getOrderId());
        orders.setDeliveryTime(new Date());
        if (flag) {
            List<Orders> twoList = ordersService.getTwoOrderByOrderId(orders.getOrderId());
            boolean sonFlag = true;
            if (twoList != null && twoList.size() > 0) ;
            for (Orders sonInfo : twoList) {
                if (sonInfo.getState() != 9) {
                    sonFlag = false;
                }
            }
            if (sonFlag) {
                orders.setState(9);
            } else {
                orders.setState(8);
            }
        } else {
            orders.setState(8);
        }
        ordersService.update(orders);


//        //查询订单信息()
//        Orders orders = ordersService.getById(orderShip.getOrderId());
       /* if (orders.getOrderClass()==1){
            lambdaQuery().eq(OrderItem::getOrderSn, orders.getOrderSn()).list()
        }
        //查询订单所有的订单项
        LambdaQueryWrapper<OrderItem> q = new LambdaQueryWrapper<>();
        q.eq(OrderItem::getOrderSn, orders.getOrderSn())
                .eq(OrderItem::getMallType, mallConfig.mallType);
        List<OrderItem> list = list(q);
        boolean flag = true;
        if (list != null && list.size() > 0) {
            for (OrderItem orderItem : list) {
                BigDecimal returnCount = orderReturnItemService.getDataByOrderItmId(orderItem.getOrderItemId(), 0);
                if (orderItem.getBuyCounts().compareTo(orderItem.getShipCounts().add(returnCount)) < 0) {
                    throw new BusinessException(500, "发货数量超出实际数量，请查看订单项为" + orderItem.getOrderItemId() + "的发货和退货数量");
                } else if (orderItem.getBuyCounts().compareTo(orderItem.getShipCounts().add(orderItem.getReturnCounts())) == 0) {
                    flag = false;
                    break;
                }else {
                    throw new BusinessException(500, "发货数量超出实际数量，请查看订单项为" + orderItem.getOrderItemId() + "的发货和退货数量");
                }
            }
        }


        //但订单状态为6是，修改发货状态为8，发货时间为此时
        if (orders.getState() == 6) {
            orders.setState(8);
            orders.setDeliveryTime(new Date());
        }

        if (flag) {
            orders.setState(9);
            //查询是否为拆分子订单
            if (orderShip.getOrderClass() == 3) {
                Orders parent = ordersService.getById(orders.getParentOrderId());
                if (parent.getDeliveryTime() == null) {
                    parent.setDeliveryTime(orders.getDeliveryTime());
                    parent.setState(8);
                }

                boolean sonFlag = true;
                //查询拆分订单的所有子订单 （根据父级id）
                List<Orders> twoList = ordersService.getTwoOrderByOrderId(orders.getParentOrderId());
                if (twoList != null && twoList.size() > 0) {
                    for (Orders son : twoList) {
                        if (son.getOrderId() != orders.getOrderId()) {
                            if (son.getState() < 9) {
                                sonFlag = flag;
                                break;
                            }
                        }

                    }
                }
                if (sonFlag) {
                    parent.setState(9);
                }
                ordersService.update(parent);
            }
        }*/
//        ordersService.update(orders);
    }


    @Override
    public List<OrderItem> findByOrderIdList(String orderId) {
        LambdaQueryWrapper<OrderItem> q = new LambdaQueryWrapper<>();
        q.eq(OrderItem::getOrderId, orderId);
        List<OrderItem> list = list(q);
        return list;


    }

    @Override

    public OrderItem getOrderItemByParentId(String parentOrderItemId) {
        LambdaQueryWrapper<OrderItem> q = new LambdaQueryWrapper<>();
        q.eq(OrderItem::getParentOrderItemId, parentOrderItemId);
        OrderItem sonOrderItem = getOne(q);
        return sonOrderItem;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateReturnState(OrderItem orderItem) {
        //假如订单项为二级订单，修改二级订单和主订单的退货数量
        String orderItemId = orderItem.getOrderItemId();
        if (orderItem.getParentOrderItemId() != null) {
            OrderItem byId = super.getById(orderItem.getParentOrderItemId());
            byId.setReturnCounts(orderItem.getReturnCounts());
            byId.setReturnState(1);
            super.updateById(byId);
        } else {
            //假如订单项为主订单，修改二级订单和主订单的退货数量
            OrderItem son = getOrderItemByParentId(orderItemId);
            //如果二级订单不存在，只修改普通订单
            if (son != null) {
                son.setReturnCounts(orderItem.getReturnCounts());
                son.setReturnState(1);
                super.updateById(son);
            }
        }
        //修改普通订单
        orderItem.setReturnState(1);
        super.updateById(orderItem);


    }

    /**
     * 查询可以选择的竞价采购的零星采购多供方订单明细
     *
     * @param jsonObject
     * @return
     */
    @Override
    public PageUtils listBidingOrderItemsList(JSONObject jsonObject) {
        Integer page = (Integer) jsonObject.get("page");
        Integer limit = (Integer) jsonObject.get("limit");
        String keywords = (String) jsonObject.get("keywords");
        String orderId = (String) jsonObject.get("orderId");
        Integer state = (Integer) jsonObject.get("state");
        Integer billType = (Integer) jsonObject.get("billType");
        Integer productType = (Integer) jsonObject.get("productType");
        List<Orders> list = ordersService.lambdaQuery().isNull(Orders::getParentOrderId)
                .eq(Orders::getShopId, ThreadLocalUtil.getCurrentUser().getShopId())
                .eq(Orders::getOrderClass, 2)
                .eq(billType != null, Orders::getBillType, billType)
                .eq(productType == null, Orders::getProductType, 10)
                .eq(productType != null, Orders::getProductType, productType)
                .select(Orders::getOrderId).list();
        if (CollectionUtils.isEmpty(list)) {
            PageUtils pageUtils = new PageUtils();
            pageUtils.setList(new ArrayList<>());
            pageUtils.setCurrPage(page);
            pageUtils.setPageSize(limit);
            return pageUtils;
        }
        List<String> orderIds = list.stream().map(t -> t.getOrderId()).collect(Collectors.toList());
        LambdaQueryWrapper<OrderItem> q = Wrappers.lambdaQuery(OrderItem.class);
        if (StringUtils.isBlank(orderId)) {
            q.in(OrderItem::getOrderId, orderIds);
        } else {
            q.eq(OrderItem::getOrderId, orderId);
        }
        if (state == null) {
            q.in(OrderItem::getState, 2, 3);
        } else {
            if (state == 2) {
                q.eq(OrderItem::getState, 2);
            }
            if (state == 3) {
                q.eq(OrderItem::getState, 3);
            }
        }
        if (!org.springframework.util.StringUtils.isEmpty(keywords)) {
            q.and((t) -> {
                t.like(OrderItem::getProductName, keywords)
                        .or()
                        .like(OrderItem::getSkuName, keywords)
                        .or()
                        .like(OrderItem::getOrderSn, keywords);
            });
        }
        IPage<OrderItem> pageRes = this.page(
                new Query<OrderItem>().getPage(jsonObject),
                q
        );
        return new PageUtils(pageRes);
    }


    @Override
    public List<OrderItem> selectsonAllByIds(List<String> orderItemIds) {
        LambdaQueryWrapper<OrderItem> q = new LambdaQueryWrapper<>();
        q.in(OrderItem::getParentOrderItemId, orderItemIds);
        return list(q);

    }


    @Override
    public void createAmouns(OrderItem orderItem, OrderReturnItem orderReturnItem, BigDecimal returnCounts) {
        if (orderItem.getParentOrderItemId() != null) {
            //含税金额
            OrderItem main0rderItem = getById(orderItem.getParentOrderItemId());
            orderReturnItem.setOtherProductPrice(orderItem.getProductPrice());
            orderReturnItem.setProductPrice(main0rderItem.getProductPrice());

            orderReturnItem.setTotalAmount(main0rderItem.getProductPrice().multiply(returnCounts));
            orderReturnItem.setOtherRateAmount(orderItem.getProductPrice().multiply(returnCounts));


            //计算税率
            BigDecimal notRatePrice = TaxCalculator.calculateNotTarRateAmount(main0rderItem.getProductPrice(), main0rderItem.getTaxRate());
            BigDecimal otherBackNoRate = TaxCalculator.calculateNotTarRateAmount(orderReturnItem.getOtherRateAmount(), orderItem.getTaxRate());
            BigDecimal otherNoRatePrice = TaxCalculator.calculateNotTarRateAmount(orderItem.getProductPrice(), orderItem.getTaxRate());

            BigDecimal notRateAmount = TaxCalculator.noTarRateItemAmount(orderReturnItem.getTotalAmount(), notRatePrice, returnCounts, main0rderItem.getTaxRate());

            BigDecimal addOtherNoRate = otherNoRatePrice.multiply(returnCounts).setScale(2, BigDecimal.ROUND_HALF_UP);
            BigDecimal otherNoRateAmount = TaxCalculator.noTarRateItemAmount(orderReturnItem.getOtherRateAmount(), otherNoRatePrice, returnCounts, orderItem.getTaxRate());

            //不含税继而
            orderReturnItem.setNoRatePrice(notRatePrice);
            orderReturnItem.setOtherNoProductPrice(otherNoRatePrice);
            orderReturnItem.setNoRateAmount(notRateAmount);
            orderReturnItem.setOtherNoRateAmount(otherNoRateAmount);
        } else {
            OrderItem son = getOrderItemByParentId(orderItem.getOrderItemId());
            if (son != null) {
                //二级供应商商品价格和商品总价 含税 + 不含税
                orderReturnItem.setOtherProductPrice(son.getProductPrice());
                orderReturnItem.setOtherRateAmount(son.getProductPrice().multiply(returnCounts));
                BigDecimal otherNoRatePrice = TaxCalculator.calculateNotTarRateAmount(son.getProductPrice(), son.getTaxRate());
                BigDecimal otherNoRateAmount = TaxCalculator.noTarRateItemAmount(orderReturnItem.getOtherNoRateAmount(),
                        otherNoRatePrice, returnCounts,
                        son.getTaxRate());

                orderReturnItem.setOtherNoProductPrice(otherNoRatePrice);

                orderReturnItem.setOtherNoRateAmount(otherNoRateAmount);

            }
            //自营店商品价格和商品总价
            orderReturnItem.setProductPrice(orderItem.getProductPrice());
            orderReturnItem.setTotalAmount(orderItem.getProductPrice().multiply(returnCounts));
            BigDecimal notRatePrice = TaxCalculator.calculateNotTarRateAmount(orderItem.getProductPrice(), orderItem.getTaxRate());
            BigDecimal notRateAmount = TaxCalculator.noTarRateItemAmount(orderReturnItem.getTotalAmount(), orderItem.getNoRatePrice(), returnCounts, orderItem.getTaxRate());
            orderReturnItem.setNoRatePrice(notRatePrice);
            orderReturnItem.setNoRateAmount(notRateAmount);
        }
//
    }


    @Override
    public List<OrderItem> findAllByOrderId(String orderId) {
        List<OrderItem> list = lambdaQuery().eq(OrderItem::getOrderId, orderId).list();
        return list;
    }

    @Override
    public PageUtils getPlatformOrderItemCount(JSONObject jsonObject, QueryWrapper<OrderItem> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String startDate = (String) innerMap.get("startDate");
        String endDate = (String) innerMap.get("endDate");
        Integer isShop = (Integer) innerMap.get("isShop");
        q.in("o.order_class", 3);

        if (isShop != null && isShop == 1) {
            String shopId = (String) innerMap.get("shopId");
            if (shopId != null && shopId != "") {
                q.eq("o.shop_id", shopId);
                innerMap.put("shopId", shopId);
            } else {
                q.eq("o.shop_id", ThreadLocalUtil.getCurrentUser().getShopId());
                innerMap.put("shopId", ThreadLocalUtil.getCurrentUser().getShopId());
            }

        }
        q.between(StringUtils.isNotEmpty(startDate) && StringUtils.isNotEmpty(endDate), "o.flish_time", startDate, endDate);
        q.eq("o.state", 10);
        innerMap.put("mallType", mallConfig.mallType);
        innerMap.put("orderClass", 3);
        IPage<OrderItem> pages = new Query<OrderItem>().getPage(jsonObject);
        List<OrderItem> list = baseMapper.getPlatformOrderItemCount(pages, q);
        ArrayList<String> labelTitle = new ArrayList<>();
        ArrayList<Integer> count = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            for (OrderItem orderItem : list) {
                orderItem.setProfitPrice(orderItem.getTotalAmount().subtract(orderItem.getCostAmount()));
            }
            List<PlatformOrdersCountVO> countVOS = baseMapper.getPlatformWeekOrderItemCount(innerMap);
            if (CollectionUtils.isEmpty(countVOS)) {
                list.get(0).setLabelTitle(labelTitle);
                list.get(0).setCount(count);
            } else {
                // 不等于空处理数据
                for (int i = 0; i < countVOS.size(); i++) {
                    labelTitle.add((i + 1) + "周");
                    count.add(countVOS.get(i).getCount());
                }
                list.get(0).setLabelTitle(labelTitle);
                list.get(0).setCount(count);
            }
        }
        pages.setRecords(list);
        return new PageUtils(pages);
    }
    @Override
    public PageUtils getAssetNameDimensionList(JSONObject jsonObject, QueryWrapper<OrderItem> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        String date = (String) innerMap.get("date");
        String orderCountOrProfitPriceTotal = (String) innerMap.get("orderCountOrProfitPriceTotal");

        Integer isShop = (Integer) innerMap.get("isShop");
        Integer productType = (Integer) innerMap.get("productType");

        String startDate = (String) innerMap.get("startDate");
        String endDate = (String) innerMap.get("endDate");
        q.in("o.order_class", 3);
        // 店铺筛选逻辑
        if (isShop != null && isShop == 1) {
            String shopId = (String) innerMap.get("shopId");
            if (shopId != null && shopId != "") {
                q.eq("o.shop_id", shopId);
                innerMap.put("shopId", shopId);
            } else {
                q.eq("o.shop_id", ThreadLocalUtil.getCurrentUser().getShopId());
                innerMap.put("shopId", ThreadLocalUtil.getCurrentUser().getShopId());
            }

        }
        // 商品类型筛选
        if (productType != null) {
            q.eq("oi.product_type", productType);
            innerMap.put("productType", productType);
        }

        // 模糊搜索
        if (StringUtils.isNotBlank(keywords)) {
            q.and(t -> t.like("o.order_sn", keywords)
                    .or().like("oi.relevance_name", keywords)
                    .or().like("oi.product_name", keywords)
                    );
            innerMap.put("keywords", keywords);
        }
        q.between(StringUtils.isNotEmpty(startDate) && StringUtils.isNotEmpty(endDate), "o.flish_time", startDate, endDate);
        q.eq("o.state", 10);
        innerMap.put("mallType", mallConfig.mallType);
        innerMap.put("orderClass", 3);
        IPage<OrderItem> pages = new Query<OrderItem>().getPage(jsonObject);
        List<OrderItem> list = baseMapper.getPlatformOrderItemCount(pages, q);
        if (CollectionUtils.isEmpty(list)) {
            pages.setRecords(list);
            return new PageUtils(pages);
        }
        // 根据时间维度处理数据
        processDateDimension(list, date, orderCountOrProfitPriceTotal, innerMap);
        pages.setRecords(list);
        return new PageUtils(pages);
    }
    @Override
    public void getAssetNameDimensionListExcel(JSONObject jsonObject, HttpServletResponse response) {
        QueryWrapper<OrderItem> q = new QueryWrapper<>();
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        String date = (String) innerMap.get("date");
        String orderCountOrProfitPriceTotal = (String) innerMap.get("orderCountOrProfitPriceTotal");

        Integer isShop = (Integer) innerMap.get("isShop");
        Integer productType = (Integer) innerMap.get("productType");

        String startDate = (String) innerMap.get("startDate");
        String endDate = (String) innerMap.get("endDate");
        q.in("o.order_class", 3);
        // 店铺筛选逻辑
        if (isShop != null && isShop == 1) {
            String shopId = (String) innerMap.get("shopId");
            if (shopId != null && shopId != "") {
                q.eq("o.shop_id", shopId);
                innerMap.put("shopId", shopId);
            } else {
                q.eq("o.shop_id", ThreadLocalUtil.getCurrentUser().getShopId());
                innerMap.put("shopId", ThreadLocalUtil.getCurrentUser().getShopId());
            }

        }
        // 商品类型筛选
        if (productType != null) {
            q.eq("oi.product_type", productType);
            innerMap.put("productType", productType);
        }

        // 模糊搜索
        if (StringUtils.isNotBlank(keywords)) {
            q.and(t -> t.like("o.order_sn", keywords)
                    .or().like("oi.relevance_name", keywords)
                    .or().like("oi.product_name", keywords)
            );
            innerMap.put("keywords", keywords);
        }
        q.between(StringUtils.isNotEmpty(startDate) && StringUtils.isNotEmpty(endDate), "o.flish_time", startDate, endDate);
        q.eq("o.state", 10);
        innerMap.put("mallType", mallConfig.mallType);
        innerMap.put("orderClass", 3);
        List<OrderItem> list = baseMapper.getPlatformOrderItemCountExcel(q);
//        if (CollectionUtils.isEmpty(list)) {
//
//            //return new PageUtils(pages);
//        }
        for (OrderItem orderItem : list){
            orderItem.setProductTypeStr(getProductTypeName(orderItem.getProductType()));
            orderItem.setGmtCreateStr(getDateStr(orderItem.getGmtCreate()));
            orderItem.setNoRateCostAmountStr(getNoRate(orderItem.getCostAmount(), orderItem.getTaxRate()));
            orderItem.setNoRateProductPriceStr(getNoRate(orderItem.getProductPrice(), orderItem.getTaxRate()));
            orderItem.setNoRateTotalAmountStr(getNoRate(orderItem.getTotalAmount(), orderItem.getTaxRate()));
            orderItem.setNoRateProfitPriceStr(getNoRate(orderItem.getProfitPrice(), orderItem.getTaxRate()));
        }
        // 根据时间维度处理数据
        //processDateDimension(list, date, orderCountOrProfitPriceTotal, innerMap);
        String src = mallConfig.templateFormUrl;
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("dataList", list);
        try {
            ExcelForWebUtil.exportExcel(response, dataMap, "平台订单统计分析模板（物资维度）.xlsx", src, "平台订单统计分析（物资维度）.xlsx");
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            throw new BusinessException(500, "导出失败！");
        }
    }
    private String getProductTypeName(Integer productType) {
        if (productType == null) {
            return "未知类型";
        }
        switch (productType) {
            case 0:
                return "零星采购";
            case 1:
                return "大宗临购";
            case 2:
                return "周材材料";
            default:
                return "未知类型";
        }
    }
    private String getDateStr(Date date) {
        if(date == null){
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }
    private String getNoRate(BigDecimal amount,BigDecimal taxRate) {
        if (amount == null || taxRate == null){
            return null;
        }else {
            // 将税率百分比转换为小数形式（3 -> 0.03）
            BigDecimal taxRateDecimal = taxRate.divide(BigDecimal.valueOf(100), 6, BigDecimal.ROUND_HALF_UP);
            // 计算不含税金额：含税金额 / (1 + 税率)
            BigDecimal divisor = BigDecimal.ONE.add(taxRateDecimal);
            BigDecimal noRateAmount = amount.divide(divisor, 2, BigDecimal.ROUND_HALF_UP);
            return String.valueOf(noRateAmount);
        }
    }
    private void processDateDimension(List<OrderItem> records, String dateType, String orderCountOrProfitPriceTotal, Map<String, Object> innerMap) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        if ("week".equals(dateType)) {
            List<LocalDate> last7Days = getLastNDays(7);
            List<Integer> orderCounts = new ArrayList<>();
            List<BigDecimal> profitTotals = new ArrayList<>();
            List<String> labels = new ArrayList<>();

            for (LocalDate date : last7Days) {
                String startOfDay = date.atStartOfDay().format(formatter);
                String endOfDay = date.atTime(23, 59, 59).format(formatter);
                labels.add(date.format(DateTimeFormatter.ofPattern("MM月dd日")));

                if ("orderCount".equals(orderCountOrProfitPriceTotal)) {
                    orderCounts.add(getOrderCountList(startOfDay, endOfDay, innerMap));
                } else {
                    profitTotals.add(getOrderProfitPriceTotal(startOfDay, endOfDay, innerMap));
                }
            }

            OrderItem firstRecord = records.get(0);
            firstRecord.setLabelTitle(labels);
            if (!orderCounts.isEmpty()) {
                firstRecord.setCount(orderCounts);
            } else {
                firstRecord.setProfitPriceTotals(profitTotals);
            }

        } else if ("month".equals(dateType)) {
            List<String> monthRanges = getRecentWeekRangesWithTime(5); // 示例：获取最近5周
            List<Integer> orderCounts = new ArrayList<>();
            List<BigDecimal> profitTotals = new ArrayList<>();
            List<String> labels = new ArrayList<>();

            for (String range : monthRanges) {
                String[] parts = range.split("~");
                String start = parts[0].split(" ")[0];
                String end = parts[1].split(" ")[0];

                labels.add(start + "~" + end);

                if ("orderCount".equals(orderCountOrProfitPriceTotal)) {
                    orderCounts.add(getOrderCountList(start, end, innerMap));
                } else {
                    profitTotals.add(getOrderProfitPriceTotal(start, end, innerMap));
                }
            }

            OrderItem firstRecord = records.get(0);
            firstRecord.setLabelTitle(labels);
            if (!orderCounts.isEmpty()) {
                firstRecord.setCount(orderCounts);
            } else {
                firstRecord.setProfitPriceTotals(profitTotals);
            }

        } else if ("year".equals(dateType)) {
            List<String> yearRanges = getRecentMonths(12);
            List<Integer> orderCounts = new ArrayList<>();
            List<BigDecimal> profitTotals = new ArrayList<>();
            List<String> labels = new ArrayList<>();

            for (String range : yearRanges) {
                String[] parts = range.split("~");
                String start = parts[0];
                String end = parts[1];

                LocalDate startDate = LocalDate.parse(start, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                String label = startDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));

                labels.add(label);

                if ("orderCount".equals(orderCountOrProfitPriceTotal)) {
                    orderCounts.add(getOrderCountList(start, end, innerMap));
                } else {
                    profitTotals.add(getOrderProfitPriceTotal(start, end, innerMap));
                }
            }

            OrderItem firstRecord = records.get(0);
            firstRecord.setLabelTitle(labels);
            if (!orderCounts.isEmpty()) {
                firstRecord.setCount(orderCounts);
            } else {
                firstRecord.setProfitPriceTotals(profitTotals);
            }
        }
    }
    private List<LocalDate> getLastNDays(int n) {
        LocalDate today = LocalDate.now();
        List<LocalDate> dates = new ArrayList<>();
        for (int i = n - 1; i >= 0; i--) {
            dates.add(today.minusDays(i));
        }
        return dates;
    }
    private Integer getOrderCountList(String startDate,String entDate, Map<String, Object> innerMap) {
        innerMap.remove("startDate");
        innerMap.remove("endDate");
        innerMap.put("startDate", startDate);
        innerMap.put("endDate", entDate);
        return ordersMapper.getPlatformWeekOrdersCount(innerMap);
    }
    private BigDecimal getOrderProfitPriceTotal(String startDate,String entDate, Map<String, Object> innerMap) {
        innerMap.remove("startDate");
        innerMap.remove("endDate");
        innerMap.put("startDate", startDate);
        innerMap.put("endDate", entDate);
        return ordersMapper.getPlatformWeekOrdersProfitPriceTotal(innerMap);
    }
    /**
     * 获取从当前日期往前推 n 个月的月份列表
     * @param monthsBack 要往前推的月数
     * @return 月份列表，格式为 "yyyy年MM月"
     */
    private List<String> getRecentMonths(int monthsBack) {
        List<String> monthRanges = new ArrayList<>();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 获取当前时间
        LocalDate currentDate = LocalDate.now();

        for (int i = 0; i < monthsBack; i++) {
            // 计算当前往前推 i 个月的日期
            LocalDate date = currentDate.minusMonths(i);

            // 获取该月的第一天
            LocalDate startOfMonth = date.with(TemporalAdjusters.firstDayOfMonth());
            // 获取该月的最后一天
            LocalDate endOfMonth = date.with(TemporalAdjusters.lastDayOfMonth());

            // 设置时间为当天的开始和结束
            LocalDateTime startOfDay = startOfMonth.atStartOfDay(); // 00:00:00
            LocalDateTime endOfDay = endOfMonth.atTime(23, 59, 59); // 23:59:59

            // 格式化为字符串
            String monthRange = startOfDay.format(dateTimeFormatter) + "~" + endOfDay.format(dateTimeFormatter);
            monthRanges.add(monthRange);
        }

        return monthRanges;
    }
    /**
     * 获取从当前日期往前推 n 周的每周起止日期段
     * @param weeksBack 要往前推的周数
     * @return 每周的起止日期段列表
     */
    private List<String> getRecentWeekRangesWithTime(int weeksBack) {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        List<String> weekRanges = new ArrayList<>();
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 获取每周的起始（周一 00:00:00）和结束时间（周日 23:59:59）
        WeekFields weekFields = WeekFields.of(Locale.getDefault());

        for (int i = weeksBack; i > 0; i--) {
            // 计算当前周往前推 i 周的日期
            LocalDate date = currentDate.minusWeeks(i);

            // 获取该周的周一
            LocalDate startOfWeek = date.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
            // 获取该周的周日
            LocalDate endOfWeek = date.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));

            // 转换为当天的开始时间和结束时间
            LocalDateTime startOfDay = startOfWeek.atStartOfDay(); // 00:00:00
            LocalDateTime endOfDay = endOfWeek.atTime(23, 59, 59); // 23:59:59

            // 格式化为 "yyyy-MM-dd HH:mm:ss ~ yyyy-MM-dd HH:mm:ss"
            String weekRange = startOfDay.format(dateTimeFormatter) + "~" + endOfDay.format(dateTimeFormatter);
            weekRanges.add(weekRange);
        }

        return weekRanges;
    }
    @Override
    public PageUtils getShopManageMaterial(JSONObject jsonObject, QueryWrapper<PlatformProductFromVo> wrapper) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String shopId = (String) jsonObject.getInnerMap().get("shopId");
        if (StringUtils.isNotEmpty(shopId)) {
            wrapper.eq("o.shop_id", shopId);
        } else {
            wrapper.eq("o.shop_id", user.getShopId());
        }
        getTransactionWrapper(jsonObject, wrapper);
        Page<TransactionProductVo> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));

        List<TransactionProductVo> list = baseMapper.selectAllTransactionProduct(pages, wrapper);
        if(!list.isEmpty()){
            list.get(0).setCountAmount(baseMapper.selectAllTransactionProductAmount(wrapper));
            list.get(0).setCountNoRateAmount(baseMapper.selectAllTransactionProductNoRateAmount( wrapper));
        }
        PageUtils<TransactionProductVo> pageUtils = new PageUtils(pages);
        pageUtils.setList(list);
        return pageUtils;
    }

    @Override
    public void supplierOutputExcel(JSONObject jsonObject, QueryWrapper<PlatformProductFromVo> wrapper, HttpServletResponse response) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String shopId = (String) jsonObject.getInnerMap().get("shopId");
        if (StringUtils.isNotEmpty(shopId)) {
            wrapper.eq("o.shop_id", shopId);
        } else {
            wrapper.eq("o.shop_id", user.getShopId());
        }
        getTransactionWrapper(jsonObject, wrapper);
        Page<TransactionProductVo> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));

        List<TransactionProductVo> list = baseMapper.selectAllTransactionProduct(pages, wrapper);
        String src = mallConfig.templateFormUrl;
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("dataList", list);
        try {
            ExcelForWebUtil.exportExcel(response, dataMap, "商品交易量统计模板.xlsx", src, "商品交易量报表.xlsx");
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            throw new BusinessException(500, "导出失败！");
        }


    }

//    @Override
//    public void supplierOutputExcel(JSONObject jsonObject, QueryWrapper<ListShipByAffirmListVO> wrapper, HttpServletResponse response) {
//        UserLogin user = ThreadLocalUtil.getCurrentUser();
//        wrapper.eq("o.shop_id", user.getShopId());
//        getTransactionWrapper(jsonObject, wrapper );
//        Page<TransactionProductVo> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
//
//        List<TransactionProductVo> list= baseMapper.selectAllTransactionProduct(pages,wrapper);
//    }

    private void getTransactionWrapper(JSONObject jsonObject, QueryWrapper<PlatformProductFromVo> wrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String productName = (String) innerMap.get("productName");
        String productSn = (String) innerMap.get("productSn");
        String startTime = (String) innerMap.get("startTime");
        String endTime = (String) innerMap.get("endTime");
        String orderSn = (String) innerMap.get("orderSn");
        String keywords = (String) innerMap.get("keywords");
        String enterpriseName = (String) innerMap.get("enterpriseName");
        List<String> ids = (ArrayList<String>) jsonObject.get("ids");
        if (ids != null && ids.size() > 0) {
            wrapper.in("oi.order_Item_id", ids);
        }
        if (StringUtils.isNotBlank(productName)) {
            wrapper.eq("oi.product_name", productName);
        }
        if (StringUtils.isNotBlank(productName)) {
            wrapper.eq("oi.product_sn", productSn);
        }
        if (StringUtils.isNotBlank(orderSn)) {
            wrapper.eq("oi.order_sn", orderSn);
        }
        if (StringUtils.isNotBlank(keywords)) {
            wrapper.like("oi.product_name", keywords)
                    .or().like("oi.order_sn", keywords)
                    .or().like("o.enterprise_name", keywords);
        }
        if (StringUtils.isNotBlank(enterpriseName)) {
            wrapper.eq("o.enterprise_name", enterpriseName);
        }
        if (StringUtils.isNotBlank(startTime)) {
            wrapper.gt("o.success_date", startTime);
        }
        if (StringUtils.isNotBlank(endTime)) {
            wrapper.lt("o.success_date", endTime);
        }
        wrapper.in("o.order_class", 1, 2);
        //wrapper.eq("o.product_type", 10);
        //wrapper.in("o.product_type", 0,1,2);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void updateOrderCostPrice(List<UpdateCostPriceDTO> dtos) {
//        BigDecimal oldTotalAmount= BigDecimal.valueOf(0);
//        BigDecimal newTotalAmount= BigDecimal.valueOf(0);
        ArrayList<OrderItem> updateList = new ArrayList<>();
        for (UpdateCostPriceDTO dto : dtos) {
            OrderItem byId = getById(dto.getOrderItemId());
            byId.setCostPrice(dto.getCostPrice());
            OrderItem orderItemByParentId = getOrderItemByParentId(dto.getOrderItemId());
            if (orderItemByParentId != null) {
                orderItemByParentId.setProfitPrice(dto.getCostPrice());
                orderItemByParentId.setTotalAmount(dto.getCostPrice().multiply(orderItemByParentId.getBuyCounts()));
                updateList.add(orderItemByParentId);
            }
            updateList.add(byId);
        }
        updateBatchById(updateList);
    }


    @Override
    public PageUtils findAllHistoryOrderItem(JSONObject jsonObject) {
        QueryWrapper<CetHistoryOrderItem> wrapper = new QueryWrapper<>();
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        Object productType = innerMap.get("productType");

        wrapper
//                .eq("o.state",8)
                .eq("o.product_type", productType)
                .in("o.order_class", 1, 2)
                .orderByDesc("o.flish_time");
        Page<CetHistoryOrderItem> pages = new Page<>((Integer) jsonObject.get("page"), (Integer) jsonObject.get("limit"));
        List<CetHistoryOrderItem> bidList = baseMapper.findAllHistoryOrderItem(pages, wrapper);
        if (bidList != null && bidList.size() > 0) {
            for (CetHistoryOrderItem info : bidList) {
                if (info.getBillType() == 1) {
                    info.setSupplyPrice("网价：" + info.getNetPrice() + "; 固定费用:" + info.getFixationPrice());
                } else if (info.getBillType() == 2) {
                    info.setSupplyPrice("出厂价：" + info.getOutFactoryPrice() + "; 运杂费:" + info.getTransportPrice());
                }
            }
        }
        pages.setRecords(bidList);
        return new PageUtils(pages);
    }

    /**
     * 修改大宗月供订单数量
     *
     * @param dtos
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDZYGOrderItemQty(List<UpdateOrderItemQtyDTO> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            throw new BusinessException("未携带参数！");
        }
        String orderId = getById(dtos.get(0).getOrderItemId()).getOrderId();
        Orders orders = ordersService.getById(orderId);
        Integer count = orderShipService.lambdaQuery().eq(OrderShip::getOrderId, orderId).count();
        if (count > 0) {
            throw new BusinessException("该订单已经生成发货单不能修改操作！");
        }
        String dtlStr = "";
        HashSet<String> orgIds = new HashSet<>();
        Orders ordersTwo = ordersService.lambdaQuery().eq(Orders::getParentOrderId, orders.getOrderId()).one();
        if (ordersTwo != null) {
            orgIds.add(ordersTwo.getSupplierId());
        }
        orgIds.add(orders.getSupplierId());
        // 校验
        for (UpdateOrderItemQtyDTO dto : dtos) {
            String orderItemId = dto.getOrderItemId();
            OrderItem orderItem = getById(orderItemId);
            if (!orderId.equals(orderItem.getOrderId())) {
                throw new BusinessException("请携带同一笔订单的订单明细！");
            }
            // 可能变小
//            if(orderItem.getBuyCounts().compareTo(dto.getQty()) == 1) {
//                throw new BusinessException("订单明细新数量不能低于源单数量！");
//            }
            BigDecimal subQty = dto.getQty().subtract(orderItem.getBuyCounts());
            OrderSelectPlan orderSelectPlan = orderSelectPlanService.lambdaQuery().eq(OrderSelectPlan::getOrderItemId, orderItemId).one();
            MaterialMonthSupplyPlanDtl dtlServiceById = materialMonthSupplyPlanDtlService.getById(orderSelectPlan.getDtlId());

            BigDecimal orderQty = dtlServiceById.getOrderQty();
            if (mallConfig.isCountPlanOrderNum == 1) {
                // TODO 计划最新统计
                BigDecimal qty = ordersService.getOrderUseCountBySelectPlanDtlIds(dtlServiceById.getPlanDtlId());
                orderQty = qty;
            }

            // 下单数量超过本期数量
            if (orderQty.add(subQty).compareTo(dtlServiceById.getThisPlanQty()) == 1) {
                // 直接让变更计划
                throw new BusinessException("物资【" + orderItem.getProductName() + "】" +
                        "订单明细所修改数量：【" + dto.getQty() + "】已超过大宗月供计划数量" +
                        "，超过数量【" + orderQty.add(subQty).subtract(dtlServiceById.getThisPlanQty()) + "】，请变更计划数量后再试！");

//                // 计划数量已用完，判断计划是否可以修改
//                QueryWrapper<MaterialMonthSupplyPlanDtl> dtlQ = new QueryWrapper<>();
//                dtlQ.eq("contract_dtl_id", dtlServiceById.getContractDtlId());
//                dtlQ.ne("state", 4);
//                dtlQ.select("sum(this_plan_qty) as count");
//                List<Map<String, Object>> maps = materialMonthSupplyPlanDtlMapper.selectMaps(dtlQ);
//                BigDecimal useQty = new BigDecimal(maps.get(0).get("count").toString());
//
//                if(subQty.add(useQty).compareTo(dtlServiceById.getSourceQty()) == 1) {
//                    throw new BusinessException("物资【"  + orderItem.getProductName()+ "】订单所选的数量超过大宗合同明细数量，请变更大宗合同后再试！");
//                }
            }

            // 通过，修改计划下单数量、计划关联表、订单数量
//            dtlServiceById.setOrderQty(orderQty.add(subQty));// 没啥用
//            materialMonthSupplyPlanDtlService.update(dtlServiceById);

            orderSelectPlan.setCount(orderSelectPlan.getCount().add(subQty));
            orderSelectPlanService.update(orderSelectPlan);

            orderItem.setBuyCounts(orderItem.getBuyCounts().add(subQty));
            dtlStr = dtlStr + "\n 物资：【" + orderItem.getProductName() +
                    "】，旧数量：" + orderItem.getBuyCounts() + "，新数量：" + orderItem.getBuyCounts().add(subQty) + "；";
            // 拿到二级订单
            OrderItem orderItemTwo = lambdaQuery().eq(OrderItem::getParentOrderItemId, orderItem.getOrderItemId()).one();
            if (orderItemTwo != null) {
                orderItemTwo.setBuyCounts(orderItem.getBuyCounts().add(subQty));
                update(orderItemTwo);
            }

            update(orderItem);

            // TODO 发送站内信
            StationMessageReceiveVO mV = new StationMessageReceiveVO();
            mV.setTitle("【通知】大宗月供订单修改。");
            mV.setState(0);
            String str = "您好！您的大宗月供订单已被收货单位修改！订单编号：【" +
                    orders.getOrderSn() + "】修改结果如下：\n" + dtlStr;
            mV.setContent(str);
            // 将 HashSet 转换为 ArrayList
            ArrayList<String> arrayList = new ArrayList<>(orgIds);
            mV.setEnterpriseIdList(arrayList);
            stationMessageService.createBatch(mV);


        }

    }

    /**
     * 修改大宗临购订单数量
     *
     * @param dtos
     * @param idStr
     * @param stringBuilder
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDZLGOrderItemQty(List<UpdateOrderItemQtyDTO> dtos, String idStr, StringBuilder stringBuilder) {
        if (CollectionUtils.isEmpty(dtos)) {
            throw new BusinessException("未携带参数！");
        }
        String orderId = getById(dtos.get(0).getOrderItemId()).getOrderId();
        Integer count = orderShipService.lambdaQuery().eq(OrderShip::getOrderId, orderId).count();
        if (count > 0) {
            throw new BusinessException("该订单已经生成发货单不能修改操作！");
        }
        OrderSelectPlan osp = orderSelectPlanService.lambdaQuery().eq(OrderSelectPlan::getOrderItemId, dtos.get(0).getOrderItemId()).one();
        HashSet<String> orgIds = new HashSet<>();
        Orders orders = ordersService.getById(orderId);
        ArrayList<Map<String, Object>> retPlanList = new ArrayList<>();
        R<Map> r = null;
        try {
            r = restTemplateUtils.getPCWP2NotParams(mallConfig.prodPcwp2Url02 + "/thirdapi/bulkRetailPlan/getBulkRetailPlanById?id=" + osp.getBillId());
        } catch (Exception e) {
            throw new BusinessException("【远程异常】：" + e.getMessage());
        }
        if (r.getCode() == null || r.getCode() != 200) {
            log.error("【远程异常】：" + r.getMessage());
            throw new BusinessException("【远程异常】：" + r.getMessage());
        }
        String s = JSON.toJSONString(r.getData());
        BulkRetailPlanEX ex = JSON.parseObject(s, BulkRetailPlanEX.class);
        List<BulkRetailPlanDtlEX> details = ex.getDetails();
        orgIds.add(orders.getSupplierId());
        String dtlStr = "";
        Orders ordersTwo = ordersService.lambdaQuery().eq(Orders::getParentOrderId, orders.getOrderId()).one();
        if (ordersTwo == null) {
            throw new BusinessException("未查询到二级订单！");
        }
        // 校验
        for (UpdateOrderItemQtyDTO dto : dtos) {
            String orderItemId = dto.getOrderItemId();
            OrderItem orderItem = getById(orderItemId);
            if (!orderId.equals(orderItem.getOrderId())) {
                throw new BusinessException("请携带同一笔订单的订单明细！");
            }
//            if (orderItem.getBuyCounts().compareTo(dto.getQty()) == 1) {
//                throw new BusinessException("订单明细新数量不能低于源单数量！");
//            }
            BigDecimal subQty = dto.getQty().subtract(orderItem.getBuyCounts());
            OrderSelectPlan orderSelectPlan = orderSelectPlanService.lambdaQuery().eq(OrderSelectPlan::getOrderItemId, orderItemId).one();

            if (mallConfig.isCountPlanOrderNum == 1) {
                // TODO 计划最新统计
                BigDecimal qty = ordersService.getOrderUseCountBySelectPlanDtlIds(orderSelectPlan.getDtlId());
                boolean flag = false;
                for (BulkRetailPlanDtlEX detail : details) {
                    String dtlId = orderSelectPlan.getDtlId();
                    if (dtlId.equals(detail.getDtlId())) {
                        flag = true;
                        // 如果大于了源计划数量
                        if (qty.add(subQty).compareTo(detail.getQuantity()) == 1) {
                            throw new BusinessException("物资【" + orderItem.getProductName() + "】" +
                                    "订单明细所修改数量：【" + dto.getQty() + "】已超过大宗临购计划数量" +
                                    "，超过数量【" + qty.add(subQty).subtract(detail.getQuantity()) + "】，请变更计划数量后再试！");
                        }
                    }
                }
                if (flag == false) {
                    throw new BusinessException("未查询到pcwp计划明细信息！");
                }
            }

            // 修改数量
            BigDecimal buyCount = orderItem.getBuyCounts().add(subQty);
            orderSelectPlan.setCount(buyCount);
            orderSelectPlanService.update(orderSelectPlan);
            dtlStr = dtlStr + "\n 物资：【" + orderItem.getProductName() +
                    "】，旧数量：" + orderItem.getBuyCounts() + "，新数量：" + buyCount + "；";

            orderItem.setBuyCounts(buyCount);

            // 重新计算金额
            BigDecimal amount = orderItem.getProductPrice().multiply(buyCount).setScale(2, RoundingMode.HALF_UP);
            orderItem.setTotalAmount(amount);

            BigDecimal addTaxAmount = orderItem.getNoRatePrice().multiply(buyCount).setScale(2, RoundingMode.HALF_UP);

            BigDecimal notBackTarRateAmount = TaxCalculator.calculateNotTarRateAmount(amount, orders.getTaxRate());
            BigDecimal notTarRateAmount = TaxCalculator.noTarRateItemAmount(amount, orderItem.getNoRateAmount(), buyCount, orders.getTaxRate());
            orderItem.setNoRateAmount(notTarRateAmount);
            BigDecimal costAmount = orderItem.getCostPrice().multiply(buyCount).setScale(2, RoundingMode.HALF_UP);
            orderItem.setCostAmount(costAmount);

            update(orderItem);

            // 拿到二级订单
            OrderItem orderItemTwo = lambdaQuery().eq(OrderItem::getParentOrderItemId, orderItem.getOrderItemId()).one();
            if (orderItemTwo == null) {
                throw new BusinessException("未查询到二级订单！");
            }
            orderItemTwo.setBuyCounts(buyCount);

            BigDecimal amount2 = orderItemTwo.getProductPrice().multiply(buyCount).setScale(2, RoundingMode.HALF_UP);
            orderItemTwo.setTotalAmount(amount2);
            orderItemTwo.setNoRateAmount(TaxCalculator.noTarRateItemAmount(amount, orderItemTwo.getNoRatePrice(), buyCount, ordersTwo.getTaxRate()));

            update(orderItemTwo);


//            // 组装反写计划
//            HashMap<String, Object> onePlanMap = new HashMap<>();
//            onePlanMap.put("dtlId", orderSelectPlan.getDtlId());
//            onePlanMap.put("billId", orderSelectPlan.getBillId());
//            onePlanMap.put("amount", orderItem.getNoRateAmount().negate());
//            onePlanMap.put("number", orderSelectPlan.getCount().negate());
//            retPlanList.add(onePlanMap);
        }


        BigDecimal actualAmount = new BigDecimal(0);
        BigDecimal noRateActualAmount = new BigDecimal(0);
        BigDecimal actualAmount2 = new BigDecimal(0);
        BigDecimal noRateActualAmount2 = new BigDecimal(0);
        BigDecimal costPriceTotal = new BigDecimal(0);

        // 重新总金额
        List<OrderItem> orderItems = lambdaQuery().eq(OrderItem::getOrderId, orderId).list();
        for (OrderItem orderItem : orderItems) {
            actualAmount = actualAmount.add(orderItem.getTotalAmount());
            noRateActualAmount = noRateActualAmount.add(orderItem.getNoRateAmount());
            costPriceTotal = costPriceTotal.add(orderItem.getCostAmount());
        }


        // 修改订单、二级订单
        orders.setActualAmount(actualAmount);
        if (mallConfig.isNotRateAmount == 1) {
            orders.setNoRateAmount(noRateActualAmount);
        } else {
            orders.setNoRateAmount(TaxCalculator.calculateNotTarRateAmount(actualAmount, orders.getTaxRate()));
        }
        orders.setCostPriceTotal(costPriceTotal);
        orders.setProfitPriceTotal(actualAmount.subtract(costPriceTotal));
        ordersService.update(orders);


        orgIds.add(ordersTwo.getSupplierId());
        List<OrderItem> orderItemsTwo = lambdaQuery().eq(OrderItem::getOrderId, ordersTwo.getOrderId()).list();
        for (OrderItem orderItem : orderItemsTwo) {
            actualAmount2 = actualAmount2.add(orderItem.getTotalAmount());
            noRateActualAmount2 = noRateActualAmount2.add(orderItem.getNoRateAmount());
        }
        ordersTwo.setActualAmount(actualAmount2);
        if (mallConfig.isNotRateAmount == 1) {
            ordersTwo.setNoRateAmount(noRateActualAmount2);
        } else {
            ordersTwo.setNoRateAmount(TaxCalculator.calculateNotTarRateAmount(actualAmount2, ordersTwo.getTaxRate()));
        }
        ordersService.update(ordersTwo);


        StationMessageReceiveVO mV = new StationMessageReceiveVO();
        mV.setTitle("【通知】大宗临购订单修改");
        mV.setState(0);
        String str = "您好！您的大宗临购订单已被收货单位修改！订单编号：【" +
                orders.getOrderSn() + "】修改结果如下：\n" + dtlStr;
        mV.setContent(str);
        // 将 HashSet 转换为 ArrayList
        ArrayList<String> arrayList = new ArrayList<>(orgIds);
        mV.setEnterpriseIdList(arrayList);
        stationMessageService.createBatch(mV);

        // TODO 后续废弃
//        UserLogin user = ThreadLocalUtil.getCurrentUser();
//        // 调用反写接口
//        HashMap<Object, Object> subPlanDDTO = new HashMap<>();
//        subPlanDDTO.put("data", retPlanList);
//        subPlanDDTO.put("keyId", idStr);
//        subPlanDDTO.put("orgId", user.getOrgId());
//
//        String content = JSON.toJSONString(subPlanDDTO);
//        stringBuilder.append(content);
//        log.warn("反写大宗临购计划接口请求参数：" + content);
//        // 发送请求
//        String url = mallConfig.prodPcwp2Url02 + "/thirdapi/bulkRetailPlan/updateBulkRetailPlanDtl";
//        LogUtil.writeInfoLog(idStr, "deleteLGOrderByOrderId", orderId, subPlanDDTO, null, OrdersServiceImpl.class);
//        R rMap = null;
//        try {
//            rMap = restTemplateUtils.postPCWP2(url, subPlanDDTO);
//        } catch (Exception e) {
//            LogUtil.writeErrorLog(idStr, "deleteLGOrderByOrderId", orderId, subPlanDDTO, null, e.getMessage(), OrdersServiceImpl.class);
//            log.error(e.getMessage());
//            throw new BusinessException("远程反写大宗临购计划服务异常：" + e.getMessage());
//        }
//        if (rMap.getCode() == null || rMap.getCode() != 200) {
//            LogUtil.writeErrorLog(idStr, "deleteLGOrderByOrderId", orderId, subPlanDDTO, rMap, rMap.getMessage(), OrdersServiceImpl.class);
//            log.error("反写大宗临购计划接口错误！返回：" + rMap);
//            throw new BusinessException("远程反写大宗临购计划服务异常：" + rMap);
//        }

    }

    /**
     * 大宗月供发货校验当前数量
     * <p>
     * 汇总订单暂估数量（订单数量需要减去商城退货数量），当订单下单数大于发货数，数量及为订单数量。当订单下单数量小于等于发货数，
     * 就需要查看每一笔发货单状态，当发货单已发货使用确认收货数量，不然则使用发货数量。把订单的发货数量相加。
     * 如果有pcwp退货数量则需要使用收货数量减去pcwp退货数量
     */
    @Override
    public void orderShipmentsQtyIsOkYG(List<OrderShipmentsQtyIsOkDTO> dtos) {
//        if (mallConfig.isCountPlanOrderNum == 1) {
        for (OrderShipmentsQtyIsOkDTO dto : dtos) {
            BigDecimal qty = dto.getQty();
            String orderItemId = dto.getOrderItemId();
            if (dto.isUpdate() == true) {
                // 如果是修改获取差异
                OrderShipDtl byId = orderShipDtlService.getById(dto.getDtlId());
                qty = qty.subtract(byId.getShipCounts());
            }
            OrderSelectPlan one = orderSelectPlanService.lambdaQuery()
                    .eq(OrderSelectPlan::getOrderItemId, orderItemId)
                    .select(OrderSelectPlan::getDtlId, OrderSelectPlan::getEquipmentName)
                    .one();
            // 获取这个订单对应的计划明细所有的数量
            String selectPlanDtlId = one.getDtlId();
            if (StringUtils.isEmpty(selectPlanDtlId)) {
                throw new BusinessException("未携带计划明细id！");
            }
            List<OrderSelectPlan> list = orderSelectPlanService.lambdaQuery()
                    .eq(OrderSelectPlan::getDtlId, selectPlanDtlId)
                    .ne(OrderSelectPlan::getOrderItemId, orderItemId)
                    .list();
            List<String> orderItemIds = list.stream().map(t -> t.getOrderItemId()).collect(Collectors.toList());
            BigDecimal p2 = orderShipDtlService.getShipCounts2(orderItemIds);
            // 之所以独立计算是为了本身是新增和其他的订单明细不一样
            BigDecimal thisQ = orderShipDtlService.getShipCountsIsThis(orderItemId, qty);
            if (p2 == null) {
                p2 = new BigDecimal(0);
            }
            p2 = p2.add(thisQ);
            MaterialMonthSupplyPlanDtl dtlServiceById = materialMonthSupplyPlanDtlService.getById(one.getDtlId());
            // 下单数量超过本期数量
            if (p2.compareTo(dtlServiceById.getThisPlanQty()) == 1) {
                throw new BusinessException("物资【" + one.getEquipmentName() + "】" +
                        "订单明细所修改数量：【" + dto.getQty() + "】已超过大宗月供计划数量" +
                        "，超过数量【" + p2.subtract(dtlServiceById.getThisPlanQty()) + "】，请变更计划数量后再试！");
            }
        }
    }

//    }

    /**
     * 大宗临购发货校验当前数量
     *
     * @param dtos
     */
    @Override
    public void orderShipmentsQtyIsOkLG(List<OrderShipmentsQtyIsOkDTO> dtos) {
//        if (mallConfig.isCountPlanOrderNum == 1) {
        //查询计划数量
        OrderSelectPlan osp = orderSelectPlanService.lambdaQuery().eq(OrderSelectPlan::getOrderItemId, dtos.get(0).getOrderItemId()).one();
        R<Map> r = null;
        try {
            r = restTemplateUtils.getPCWP2NotParams(mallConfig.prodPcwp2Url02 + "/thirdapi/bulkRetailPlan/getBulkRetailPlanById?id=" + osp.getBillId());
        } catch (Exception e) {
            throw new BusinessException("【远程异常】：" + e.getMessage());
        }
        if (r.getCode() == null || r.getCode() != 200) {
            log.error("【远程异常】：" + r.getMessage());
            throw new BusinessException("【远程异常】：" + r.getMessage());
        }
        String s = JSON.toJSONString(r.getData());
        BulkRetailPlanEX ex = JSON.parseObject(s, BulkRetailPlanEX.class);
        List<BulkRetailPlanDtlEX> details = ex.getDetails();

        for (OrderShipmentsQtyIsOkDTO dto : dtos) {
            BigDecimal qty = dto.getQty();
            String orderItemId = dto.getOrderItemId();
            if (dto.isUpdate() == true) {
                // 如果是修改获取差异
                OrderShipDtl byId = orderShipDtlService.getById(dto.getDtlId());
                qty = qty.subtract(byId.getShipCounts());
            }
            OrderSelectPlan one = orderSelectPlanService.lambdaQuery()
                    .eq(OrderSelectPlan::getOrderItemId, orderItemId)
                    .select(OrderSelectPlan::getDtlId, OrderSelectPlan::getEquipmentName)
                    .one();
            String selectPlanDtlId = one.getDtlId();
            if (StringUtils.isEmpty(selectPlanDtlId)) {
                throw new BusinessException("未携带计划明细id！");
            }
            List<OrderSelectPlan> list = orderSelectPlanService.lambdaQuery()
                    .eq(OrderSelectPlan::getDtlId, selectPlanDtlId)
                    .ne(OrderSelectPlan::getOrderItemId, orderItemId)
                    .list();
            List<String> orderItemIds = list.stream().map(t -> t.getOrderItemId()).collect(Collectors.toList());
            //旧发货数量
            BigDecimal p2 = orderShipDtlService.getShipCounts2(orderItemIds);
//       thisQ  计划可发货数量
            BigDecimal thisQ = orderShipDtlService.getShipCountsIsThis(orderItemId, qty);
            if (p2 == null) {
                p2 = new BigDecimal(0);
            }
            p2 = p2.add(thisQ);
            // 发货数量大于订单数量，校验数量
            boolean flag = false;
            for (BulkRetailPlanDtlEX detail : details) {
                String dtlId = one.getDtlId();
                if (dtlId.equals(detail.getDtlId())) {
                    flag = true;
                    // 下单数量超过本期数量
                    if (p2.compareTo(detail.getQuantity()) == 1) {
                        throw new BusinessException("物资【" + one.getEquipmentName() + "】" +
                                "数量：【" + dto.getQty() + "】已超过大宗临购计划数量" +
                                "，超过数量【" + p2.subtract(detail.getQuantity()) + "】，请变更计划数量后再试！");
                    }
                }
            }
            if (flag == false) {
                throw new BusinessException("未查询到pcwp计划明细信息！");
            }
        }
    }

//    }


    @Override
    public BigDecimal getConfirmCounts(String orderItemId) {
        OrderItem byId = getById(orderItemId);
        return byId.getConfirmCounts();
    }

    /**
     * 查询可以选择的竞价采购订单号分页
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils listBidingOrderListIds(JSONObject jsonObject, LambdaQueryWrapper<OrderItem> q) {
        Integer page = (Integer) jsonObject.get("page");
        Integer limit = (Integer) jsonObject.get("limit");
        String keywords = (String) jsonObject.get("keywords");
        String orderId = (String) jsonObject.get("orderId");
        Integer state = (Integer) jsonObject.get("state");
        String orderSn = (String) jsonObject.get("orderSn");
        Integer billType = (Integer) jsonObject.get("billType");
        Integer productType = (Integer) jsonObject.get("productType");
        List<Orders> list = ordersService.lambdaQuery().isNull(Orders::getParentOrderId)
                .eq(Orders::getShopId, ThreadLocalUtil.getCurrentUser().getShopId())
                .eq(Orders::getOrderClass, 2)
                .eq(billType != null, Orders::getBillType, billType)
                .eq(StringUtils.isNotBlank(orderSn), Orders::getOrderSn, orderSn)
                .eq(productType == null, Orders::getProductType, 10)
                .eq(productType != null, Orders::getProductType, productType)
                .select(Orders::getOrderId).list();
        if (CollectionUtils.isEmpty(list)) {
            PageUtils pageUtils = new PageUtils();
            pageUtils.setList(new ArrayList<>());
            pageUtils.setCurrPage(page);
            pageUtils.setPageSize(limit);
            return pageUtils;
        }
        List<String> orderIds = list.stream().map(t -> t.getOrderId()).collect(Collectors.toList());
        if (StringUtils.isBlank(orderId)) {
            q.in(OrderItem::getOrderId, orderIds);
        } else {
            q.eq(OrderItem::getOrderId, orderId);
        }
        if (state == null) {
            q.in(OrderItem::getState, 2, 3);
        } else {
            if (state == 2) {
                q.eq(OrderItem::getState, 2);
            }
            if (state == 3) {
                q.eq(OrderItem::getState, 3);
            }
        }
        if (!org.springframework.util.StringUtils.isEmpty(keywords)) {
            q.and((t) -> {
                t.like(OrderItem::getProductName, keywords)
                        .or()
                        .like(OrderItem::getSkuName, keywords)
                        .or()
                        .like(OrderItem::getOrderSn, keywords);
            });
        }
        q.orderByDesc(OrderItem::getGmtCreate);
        q.select(OrderItem::getOrderId, OrderItem::getOrderSn, OrderItem::getGmtCreate);
        q.groupBy(OrderItem::getOrderId, OrderItem::getOrderSn, OrderItem::getGmtCreate);
        IPage<OrderItem> pageRes = this.page(
                new Query<OrderItem>().getPage(jsonObject),
                q
        );
        return new PageUtils(pageRes);
    }

    /**
     * 根据订单id获取可以发货的数量
     *
     * @param orderItemId
     * @return
     */
    @Override
    public BigDecimal getSendProductMaxNumByOrderItemId(String orderItemId) {
        // 购买数量-商城退货数量 -（所有单子未收货时发货数量，已收货就是收货数量的所有单据合计）+pcwp退货 = 可发货数量
        OrderItem orderItem = getById(orderItemId);
        BigDecimal buyCounts = orderItem.getBuyCounts();
        Orders byId = ordersService.getById(orderItem.getOrderId());
        if (byId.getProductType() == 12 || byId.getProductType() == 13) {
            buyCounts = buyCounts.add(buyCounts.multiply(BigDecimal.valueOf(0.1)).setScale(4, BigDecimal.ROUND_HALF_UP));
        }
        BigDecimal maxQty = buyCounts.subtract(orderItem.getReturnCounts()).add(orderItem.getPcwpReturn());
        List<OrderShipDtl> dtls = orderShipDtlService.lambdaQuery()
                .eq(OrderShipDtl::getOrderItemId, orderItem.getOrderItemId())
                .select(OrderShipDtl::getBillId, OrderShipDtl::getShipCounts, OrderShipDtl::getShipNum).list();
        if (!CollectionUtils.isEmpty(dtls)) {
            for (OrderShipDtl dtl : dtls) {
                OrderShip orderShip = orderShipService.lambdaQuery().eq(OrderShip::getBillId, dtl.getBillId())
                        .select(OrderShip::getBillId, OrderShip::getType).one();
                // 收货
                if (orderShip.getType() == 2) {
                    maxQty = maxQty.subtract(dtl.getShipNum());
                } else {
                    maxQty = maxQty.subtract(dtl.getShipCounts());
                }
            }
        }
        if (maxQty.compareTo(BigDecimal.ZERO) == -1) {
            throw new BusinessException("统计可发货数量错误！");
        }

        return maxQty;
    }

    /**
     * 订单数量-商城退货 > 有效数量（有收货使用收获，未收货使用发货）-pcwp退货数量时，为未全部发货
     *
     * @param orderItemId
     * @return
     */
    @Override
    public boolean getIsNotAllSendProduct(String orderItemId) {
        OrderItem one = lambdaQuery().eq(OrderItem::getOrderItemId, orderItemId).one();
        List<OrderShipDtl> dtls = orderShipDtlService.lambdaQuery()
                .eq(OrderShipDtl::getOrderItemId, orderItemId)
                .select(OrderShipDtl::getBillId, OrderShipDtl::getShipCounts, OrderShipDtl::getShipNum).list();
        if (CollectionUtils.isEmpty(dtls)) {
            OrderItem oneOrder = lambdaQuery().eq(OrderItem::getParentOrderItemId, orderItemId)
                    .select(OrderItem::getOrderId, OrderItem::getOrderItemId).one();
            if (oneOrder != null) {
                dtls = orderShipDtlService.lambdaQuery()
                        .eq(OrderShipDtl::getOrderItemId, oneOrder.getOrderItemId())
                        .select(OrderShipDtl::getBillId, OrderShipDtl::getShipCounts, OrderShipDtl::getShipNum).list();
            }
        }
        // 商城退货
        BigDecimal mReturnCounts = one.getReturnCounts();
        // pcwp退货
        BigDecimal pcwpRetuen = one.getPcwpReturn();
        // 有效数量
        BigDecimal qty = new BigDecimal(0);
        // 购买数量
        BigDecimal buyQty = one.getBuyCounts();
        if (!CollectionUtils.isEmpty(dtls)) {
            // 已经发货完成
            for (OrderShipDtl dtl : dtls) {
                OrderShip orderShip = orderShipService.lambdaQuery().eq(OrderShip::getBillId, dtl.getBillId())
                        .select(OrderShip::getBillId, OrderShip::getType).one();
                // 收货
                if (orderShip.getType() == 2) {
                    qty = qty.add(dtl.getShipNum());
                } else {
                    qty = qty.add(dtl.getShipCounts());
                }
            }
        }
        // 订单数量-商城退货 > 有效数量（有收货使用收获，未收货使用发货）-pcwp退货数量时，为未全部发货
        if (buyQty.subtract(mReturnCounts).compareTo(qty.subtract(pcwpRetuen)) > 0) {
            return true;
        } else {
            return false;
        }
    }

    //修改所有商品的销量
    @Override
    public void upProductSoldNum() {
        /**
         * 最简sql
         * 	UPDATE product_sku ps
         * INNER JOIN (
         *     SELECT oi.product_id, SUM(oi.buy_counts) AS soldNum
         *     FROM order_item oi
         *     LEFT JOIN orders o ON oi.order_id = o.order_id
         *     WHERE o.order_class !=3 AND o.is_delete = 0 AND oi.product_sn IS NOT NULL AND oi.is_delete = 0
         *     GROUP BY oi.product_sn
         * ) t ON ps.product_id = t.product_id
         * SET ps.sold_num = t.soldNum
         */
        //1.查询所有的订单项（订单项关联订单）根据商品id进行分组，得到卖出的商品和（sum(buy_counts)）总购买数量
        // 1.排除大宗月供商品（product_type=12）
        //2.排除所有子订单
        //3.排除所有删除的订单
        List<HashMap<String, Object>> list = baseMapper.selectProductSoldNumBySn();
        ArrayList<ProductSku> productSkus = new ArrayList<>();
        for (HashMap<String, Object> product : list) {
            ProductSku productSku = productSkuService.getById(product.get("productId").toString());
            if (productSku != null) {
                productSku.setSoldNum((BigDecimal) product.get("soldNum"));
                productSkus.add(productSku);
            }
        }
        productSkuService.updateBatchById(productSkus);
    }


    @Override
    public void createOneAmouns(OrderItem main0rderItem, OrderReturnItem orderReturnItem, BigDecimal returnCounts) {
        orderReturnItem.setProductPrice(main0rderItem.getProductPrice());
        orderReturnItem.setTotalAmount(main0rderItem.getProductPrice().multiply(returnCounts));
        //计算税率
        BigDecimal notRatePrice = TaxCalculator.calculateNotTarRateAmount(main0rderItem.getProductPrice(), main0rderItem.getTaxRate());
        BigDecimal notRateAmount = TaxCalculator.noTarRateItemAmount(orderReturnItem.getTotalAmount(), notRatePrice, returnCounts, main0rderItem.getTaxRate());

        //不含税继而
        orderReturnItem.setNoRatePrice(notRatePrice);
        orderReturnItem.setNoRateAmount(notRateAmount);
    }



}

