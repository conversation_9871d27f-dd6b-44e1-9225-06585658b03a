package scrbg.meplat.mall.pcwp.third;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import scrbg.meplat.mall.pcwp.FeignFormDataConfig;
import scrbg.meplat.mall.pcwp.PcwpClient;
import scrbg.meplat.mall.pcwp.PcwpRes;
/**
 * 第三方接口服务
 */
@FeignClient(name = "pcwp-thirdapi-upload-service", url = "${mall.prodPcwp2Url02}", configuration=FeignFormDataConfig.class)
//@Profile("!mock-pcwp")
public interface PcwpThirdApiUploadClient extends PcwpClient{

    /**
     * 对象上传
     * @return
     */
    @PostMapping(value = "/thirdapi/material/uploader", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    PcwpRes<Void> materialUploade(@RequestPart("files") MultipartFile[] files,
                                                @RequestParam("relationId") String relationId,
                                                @RequestParam("bucketName") String bucketName,
                                                @RequestParam("orgCode") String shortCode,
                                                @RequestHeader("token") String token,
                                                @RequestHeader("syscode") String syscode);
}
