<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

<!--    <parent>-->
<!--        <groupId>scrbg.meplat.mall</groupId>-->
<!--        <artifactId>mall-material</artifactId>-->
<!--        <version>0.0.1-SNAPSHOT</version>-->
<!--    </parent>-->


    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.2.8.RELEASE</version>
    </parent>

    <groupId>scrbg.meplat.mall</groupId>
    <artifactId>mall-material-service</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <modelVersion>4.0.0</modelVersion>

    <dependencies>
        <dependency>
            <groupId>ch.ethz.ganymed</groupId>
            <artifactId>ganymed-ssh2</artifactId>
            <version>262</version>
        </dependency>
        <!-- word导出 -->
        <dependency>
            <groupId>org.docx4j</groupId>
            <artifactId>docx4j-export-fo</artifactId>
            <version>6.1.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>1.10.0</version>
        </dependency>
        <!-- excel2pdf -->
        <dependency>
            <groupId>com.lowagie</groupId>
            <artifactId>itext</artifactId>
            <version>2.1.7</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.9</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
            <version>5.2.0</version>
        </dependency>
        <!--EasyExcel相关依赖-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.0.5</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.19</version>
        </dependency>
        <!--二维码生成-->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.5.2</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>3.5.2</version>
        </dependency>






        <!--获取姓名或者名称的首字母或者全拼-->
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.0</version>
        </dependency>
        <!--mybatis plus引用-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>3.4.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
            <version>2.2</version>
        </dependency>


        <!--common-->
        <dependency>
            <groupId>com.scrbg.pcwp2</groupId>
            <artifactId>common</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>druid-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.nacos</groupId>
                    <artifactId>nacos-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
            <version>1.2.1</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>1.1.21</version>
            <exclusions>
                <exclusion>
                    <artifactId>tools</artifactId>
                    <groupId>com.sun</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jconsole</artifactId>
                    <groupId>com.sun</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.antlr</groupId>
            <artifactId>ST4</artifactId>
            <version>4.3</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>23.0.0</version>
            <scope>compile</scope>
        </dependency>
        <!--Spring Cache，使用注解简化开发-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        <!--redis启动器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-data-22</artifactId>
            <version>3.16.8</version>
        </dependency>
        <dependency>
            <groupId>p6spy</groupId>
            <artifactId>p6spy</artifactId>
            <version>3.8.7</version>
        </dependency>
        <!--引入jwt依赖-->
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
            <version>3.4.0</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>0.9.0</version>
        </dependency>
        <!--AMQP依赖，包含RabbitMQ-->
        <!--        <dependency>-->
        <!--            <groupId>org.springframework.boot</groupId>-->
        <!--            <artifactId>spring-boot-starter-amqp</artifactId>-->
        <!--        </dependency>-->
        <!--test-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-rabbit</artifactId>
        </dependency>
        <dependency>
            <groupId>net.roseboy</groupId>
            <artifactId>classfinal-core</artifactId>
            <version>1.2.1</version>
        </dependency>

        <dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<scope>runtime</scope>
			<optional>true</optional>
		</dependency>
        <dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>
    </dependencies>

    <profiles>
        <profile>
            <!-- 本地开发环境 -->
            <id>dev</id>
            <properties>
                <activeProfile>dev</activeProfile>
                <skipDocker>true</skipDocker>
                <!--                <dockerHost>http://***************:2375</dockerHost>-->
                <dockerHost>http://*************:2375</dockerHost>
            </properties>
            <!--<activation>
                <activeByDefault>true</activeByDefault>
            </activation>-->
        </profile>
        <profile>
            <!-- 测试环境 -->
            <id>test</id>
            <properties>
                <activeProfile>test</activeProfile>
                <skipDocker>true</skipDocker>
                <!--                <dockerHost>http://**************:2375</dockerHost>-->
                <dockerHost>http://*************:2375</dockerHost>
            </properties>
        </profile>
        <profile>
            <!-- 生产环境 -->
            <id>prod</id>
            <properties>
                <activeProfile>prod</activeProfile>
                <skipDocker>true</skipDocker>
                <dockerHost>http://***************:2375</dockerHost>
            </properties>
        </profile>
    </profiles>

    <build>
        <plugins>
<!--   proguard         -->
<!--            <plugin>-->
<!--                <groupId>com.github.wvengen</groupId>-->
<!--                <artifactId>proguard-maven-plugin</artifactId>-->
<!--                <version>2.6.0</version>-->
<!--                <executions>-->
<!--                    &lt;!&ndash; 以下配置说明执行mvn的package命令时候，会执行proguard&ndash;&gt;-->
<!--                    <execution>-->
<!--                        <phase>package</phase>-->
<!--                        <goals>-->
<!--                            <goal>proguard</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--                <configuration>-->
<!--                    &lt;!&ndash; 就是输入Jar的名称，我们要知道，代码混淆其实是将一个原始的jar，生成一个混淆后的jar，那么就会有输入输出。 &ndash;&gt;-->
<!--                    <injar>${project.build.finalName}.jar</injar>-->
<!--                    &lt;!&ndash; 输出jar名称，输入输出jar同名的时候就是覆盖，也是比较常用的配置。 &ndash;&gt;-->
<!--                    <outjar>${project.build.finalName}.jar</outjar>-->
<!--                    &lt;!&ndash; 是否混淆 默认是true &ndash;&gt;-->
<!--                    <obfuscate>true</obfuscate>-->
<!--                    &lt;!&ndash; 配置一个文件，通常叫做proguard.cfg,该文件主要是配置options选项，也就是说使用proguard.cfg那么options下的所有内容都可以移到proguard.cfg中 &ndash;&gt;-->
<!--                    <proguardInclude>${project.basedir}/proguard.cfg</proguardInclude>-->
<!--                    &lt;!&ndash; 额外的jar包，通常是项目编译所需要的jar &ndash;&gt;-->
<!--                    <libs>-->
<!--                        <lib>${java.home}/lib/rt.jar</lib>-->
<!--                        <lib>${java.home}/lib/jce.jar</lib>-->
<!--                        <lib>${java.home}/lib/jsse.jar</lib>-->
<!--                    </libs>-->
<!--                    &lt;!&ndash; 对输入jar进行过滤比如，如下配置就是对META-INFO文件不处理。 &ndash;&gt;-->
<!--                    <inLibsFilter>!META-INF/**,!META-INF/versions/9/**.class</inLibsFilter>-->
<!--                    &lt;!&ndash; 这是输出路径配置，但是要注意这个路径必须要包括injar标签填写的jar &ndash;&gt;-->
<!--                    <outputDirectory>${project.basedir}/target</outputDirectory>-->
<!--                    &lt;!&ndash;这里特别重要，此处主要是配置混淆的一些细节选项，比如哪些类不需要混淆，哪些需要混淆&ndash;&gt;-->
<!--                    <options>-->
<!--                        &lt;!&ndash; 可以在此处写option标签配置，不过我上面使用了proguardInclude，故而我更喜欢在proguard.cfg中配置 &ndash;&gt;-->
<!--                    </options>-->
<!--                </configuration>-->
<!--            </plugin>-->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <!-- https://gitee.com/roseboy/classfinal -->
                <groupId>net.roseboy</groupId>
                <artifactId>classfinal-maven-plugin</artifactId>
                <version>1.2.1</version>
                <configuration>
                    <password>#</password><!--加密打包之后pom.xml会被删除，不用担心在jar包里找到此密码-->
                    <packages>scrbg.meplat.mall</packages>
                    <cfgfiles>application.yml</cfgfiles>
                    <excludes>org.spring</excludes>
<!--                    <libjars>a.jar,b.jar</libjars>-->
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>classFinal</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
<!--            <plugin>-->
<!--                <groupId>org.springframework.boot</groupId>-->
<!--                <artifactId>spring-boot-maven-plugin</artifactId>-->
<!--            </plugin>-->
        </plugins>
    </build>

<!--    <build>-->
<!--        <plugins>-->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-compiler-plugin</artifactId>-->
<!--                <configuration>-->
<!--                    <source>1.8</source> &lt;!&ndash;指明源码用的Jdk版本&ndash;&gt;-->
<!--                    <target>1.8</target> &lt;!&ndash;指明打包后的Jdk版本&ndash;&gt;-->
<!--                </configuration>-->
<!--            </plugin>-->
<!--        </plugins>-->
<!--        <resources>-->
<!--            <resource>-->
<!--                <directory>src/main/resources</directory>-->
<!--                <excludes>-->
<!--                    &lt;!&ndash;打包时需要被排除的文件&ndash;&gt;-->
<!--                    <exclude>application.yml</exclude>-->
<!--                    <exclude>application-dev.yml</exclude>-->
<!--                    <exclude>application-prod.yml</exclude>-->
<!--                    <exclude>application-test.yml</exclude>-->
<!--                </excludes>-->
<!--            </resource>-->
<!--        </resources>-->
<!--    </build>-->
</project>
